/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-08-29 10:35:41
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-26 18:40:11
 * @FilePath: /busienginewebsite/app/router.options.ts
 * @Description:
 */
import type { RouterConfig } from '@nuxt/schema'
import { type RouteRecordRaw } from 'vue-router'

const baseRouter: RouteRecordRaw[] = [
  {
    name: 'home',
    path: '/',
    meta: { title: '首页', icon: '' },
    component: () => import('~/pages/home/<USER>'),
  },
  {
    name: 'product',
    path: '/product',
    meta: { title: '产品功能', icon: '' },
    redirect: '/product/lite',
    component: () => import('~/pages/product/index.vue'),
    children: [
      {
        name: 'productLite',
        path: '/product/lite',
        component: () => import('~/pages/product/lite/index.vue'),
        meta: { title: '商瞳·个人洞察工具', icon: '' },
      },
      {
        name: 'productEia',
        path: '/product/eia',
        component: () => import('~/pages/product/eia/index.vue'),
        meta: { title: '商瞳·B2B市场洞察平台', icon: '' },
      },
      {
        name: 'ProductInterface',
        path: '/product/interface',
        component: () => import('~/pages/product/interface/index.vue'),
        meta: { title: '商瞳·数据API', icon: '' },
      },
    ],
    //
  },
  {
    name: 'solution',
    path: '/solution',
    meta: { title: '解决方案', icon: '' },
    redirect: '/solution/industry',
    component: () => import('~/pages/solution/index.vue'),
    children: [
      {
        name: 'SolutionIndustry',
        path: '/solution/industry',
        component: () => import('~/pages/solution/industry/index.vue'),
        meta: { title: '面向行业市场', icon: '' },
      },
      {
        name: 'SolutionBusiness',
        path: '/solution/business',
        component: () => import('~/pages/solution/business/index.vue'),
        meta: { title: '面向商业市场', icon: '' },
      },
      {
        name: 'SolutionDistribution',
        path: '/solution/distribution',
        component: () => import('~/pages/solution/distribution/index.vue'),
        meta: { title: '面向分销市场', icon: '' },
      },
      {
        name: 'SolutionOverseas',
        path: '/solution/overseas',
        component: () => import('~/pages/solution/overseas/index.vue'),
        meta: { title: '面向海外市场', icon: '' },
      },
      {
        name: 'SolutionInvestmentAttraction',
        path: '/solution/investmentAttraction',
        component: () => import('~/pages/solution/investmentAttraction/index.vue'),
        meta: { title: '产业招商方案', icon: '' },
      },
    ],
  },
  {
    name: 'case',
    path: '/case',
    meta: { title: '客户案例', icon: '' },
    component: () => import('~/pages/case/index.vue'),
  },
  {
    name: 'about',
    path: '/about',
    meta: { title: '关于我们', icon: '' },
    component: () => import('~/pages/about/index.vue'),
  },
  {
    name: 'becomingPartners',
    path: '/becomingPartners',
    meta: { title: '成为伙伴', icon: '' },
    component: () => import('~/pages/becomingPartners/index.vue'),
  },
]
const router = {
  routes: (_routes) => {
    return [
      ...baseRouter,
      {
        name: '403',
        path: '/403',
        component: () => import('~/pages/error/403.vue'),
      },
      {
        name: '404',
        path: '/404',
        component: () => import('~/pages/error/404.vue'),
      },
      {
        name: '500',
        path: '/500',
        component: () => import('~/pages/error/500.vue'),
      },
      {
        name: 'NotFound',
        path: '/:pathMatch(.*)*',
        redirect: '/404',
      },
    ]
  },
} satisfies RouterConfig

export { baseRouter }
export default router
