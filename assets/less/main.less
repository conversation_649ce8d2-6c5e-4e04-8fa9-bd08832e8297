/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:12:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-09-11 18:52:40
 * @FilePath: /busienginewebsite/src/assets/less/main.less
 * @Description: 
 */
@import './base.css';
@import './resize.css';
@import './media.less';

@primary-color: #5851a2; // 全局主色
/* color palette from <https://github.com/vuejs/theme> */
:root {
  /* prettier-ignore */
  --nav-menu__height: 68PX;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition: color 0.5s, background-color 0.5s;
  line-height: 1.6;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// .childrenMenuItem {
//   span {
//     transition: all 0.3s ease;
//     transform: translateX(0px);
//   }
//   &:hover {
//     span {
//       transform: translateX(4px);
//     }
//   }
// }

.ant-layout-header {
  line-height: 1;
  box-shadow: 0px 3px 7px 1px rgba(0, 77, 138, 0.05);
  position: fixed;
  top: 0px;
  width: 100%;
}

.ant-menu-overflow-item {
}
.ant-menu-title-content {
  font-weight: 600;
  font-size: 15px;
}

// 文字悬浮显示主色
.hoverPrimaryColor {
  &:hover {
    cursor: pointer;
    color: @primary-color !important;
  }
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;

  .media-query(xs,{max-width: 540px;padding: 0 32px;});
  .media-query(sm,{max-width: 820px;padding: 0 32px;});
  .media-query(md,{padding: 0px;});
  .media-query(lg,{max-width: 960px;});
  .media-query(xl,{max-width: 1140px;});
  .media-query(xxl,{max-width: 1320px;});

  .header {
    padding: 80px;
    .headerTitle {
      display: block;
      text-align: center;
      color: #000000;
      font-size: 30px;
      line-height: 1.2;
      font-weight: 500;
      text-align: center;
    }
    .headerDesc {
      display: block;
      text-align: center;
      font-size: 18px;
      margin-top: 16px;
      color: #666666;
    }
  }
}
.container-bg1 {
  background-color: #ffffff;
}
.container-bg2 {
  background-color: #f7f8fb;
}
.fw-bold {
  font-weight: 700 !important;
}
.text-start {
  text-align: left !important;
}
.text-end {
  text-align: right !important;
}
.text-center {
  text-align: center !important;
}
.justify-content-start {
  justify-content: flex-start !important;
}
.justify-content-end {
  justify-content: flex-end !important;
}
.justify-content-center {
  justify-content: center !important;
}
.justify-content-between {
  justify-content: space-between !important;
}
.justify-content-around {
  justify-content: space-around !important;
}
.justify-content-evenly {
  justify-content: space-evenly !important;
}
.align-items-start {
  align-items: flex-start !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.align-items-center {
  align-items: center !important;
}
.align-items-baseline {
  align-items: baseline !important;
}
.align-items-stretch {
  align-items: stretch !important;
}
