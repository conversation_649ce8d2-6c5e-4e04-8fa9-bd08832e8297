// 定义变量 https://www.antdv.com/components/grid-cn/#col
@screen-xs: 576Px;
@screen-sm: 576Px;
@screen-md: 768Px;
@screen-lg: 992Px;
@screen-xl: 1200Px;
@screen-xxl: 1600Px;

.media-query(@type, @style) when (@type=xs) {
  @media (max-width: @screen-xs) {
    @style();
  }
}
.media-query(@type, @style) when (@type=sm) {
  @media (min-width: @screen-sm) {
    @style();
  }
}
.media-query(@type, @style) when (@type=md) {
  @media (min-width: @screen-md) {
    @style();
  }
}
.media-query(@type, @style) when (@type=lg) {
  @media (min-width: @screen-lg) {
    @style();
  }
}
.media-query(@type, @style) when (@type=xl) {
  @media (min-width: @screen-xl) {
    @style();
  }
}
.media-query(@type, @style) when (@type=xxl) {
  @media (min-width: @screen-xxl) {
    @style();
  }
}
