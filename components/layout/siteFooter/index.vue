<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:46:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 10:33:26
 * @FilePath: /busienginewebsite/components/layout/siteFooter/index.vue
 * @Description: 
-->
<template>
  <a-layout-footer class="siteFooter">
    <div class="footerBanner text-center fw-bold">
      <div class="footerBanner__text">{{ route.path === '/becomingPartners' ? '申请成为合作伙伴' : '立刻申请，开始数字化体验之旅' }}</div>
      <a-button size="large" class="fw-bold" @click="bus.emit('showModal')">
        {{ route.path === '/becomingPartners' ? '申请合作' : '申请体验' }}
      </a-button>
    </div>

    <div>
      <!-- <footer class="footer-area"> -->
      <div class="container">
        <a-row>
          <a-col :xs="{ span: 12 }" :sm="{ span: 12 }" :lg="{ span: 6 }">
            <div class="single-widget">
              <h3 class="widget-title"></h3>
              <div class="widget-footer-info">
                <a href="/product/eia">
                  <span><i class="far fa-angle-double-right"></i>产品</span>
                </a>

                <a href="/product/eia">
                  <span><i class="far fa-angle-double-right"></i>B2B市场洞察平台</span>
                </a>

                <a href="/product/interface">
                  <span> <i class="far fa-angle-double-right"></i>数据API </span>
                </a>
              </div>
            </div>
          </a-col>

          <a-col :xs="{ span: 12 }" :sm="{ span: 12 }" :lg="{ span: 6 }">
            <div class="single-widget">
              <h3 class="widget-title"></h3>
              <div class="widget-footer-info">
                <a href="/solution/business">
                  <span><i class="far fa-angle-double-right"></i>解决方案</span>
                </a>
                <a href="/solution/business">
                  <span><i class="far fa-angle-double-right"></i>行业市场</span>
                </a>
                <a href="/solution/distribution">
                  <span><i class="far fa-angle-double-right"></i>商业市场</span>
                </a>
                <a href="/solution/industry">
                  <span><i class="far fa-angle-double-right"></i>分销市场</span>
                </a>
                <a href="/solution/investmentAttraction">
                  <span><i class="far fa-angle-double-right"></i>海外市场</span>
                </a>
                <a href="/solution/overseas">
                  <span><i class="far fa-angle-double-right"></i>招商方案</span>
                </a>

                <!-- <a href="#"><i class="far fa-angle-double-right"></i>加入我们</a> -->
              </div>
            </div>
          </a-col>

          <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :lg="{ span: 12 }">
            <div class="single-widget">
              <h3 class="widget-title"></h3>
              <a-row class="widget-content">
                <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 18 }">
                  <ul class="f-contact-inner">
                    <li>
                      <i class="far fa-envelope-open"></i>
                      <iconfontIcon icon="icon-mail" class="far fa-envelope-open" />
                      <a href="mailto:<EMAIL>">联系邮箱</a>
                      <br />
                      <EMAIL>
                    </li>
                    <li>
                      <iconfontIcon icon="icon-location" class="far fa-envelope-open" />
                      <a>珠海办公室</a>
                      <br />
                      广东省珠海市横琴新区环岛东路3000号ICC横琴国际商务中心10楼
                    </li>
                    <li>
                      <iconfontIcon icon="icon-location" class="far fa-envelope-open" />
                      <a>广州办公室</a>
                      <br />
                      广东省广州市天河区黄埔大道西120号高志大厦23楼
                    </li>
                  </ul>
                </a-col>
                <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 6 }">
                  <a-row class="f-contact-qrCode">
                    <a-col :xs="{ span: 12 }" :sm="{ span: 12 }" :md="{ span: 24 }">
                      <a-image :width="100" src="/images/footer/wechatQrCode.jpg" :preview="false" />
                      <p class="text-white">官方服务号</p>
                    </a-col>
                    <a-col :xs="{ span: 12 }" :sm="{ span: 12 }" :md="{ span: 24 }">
                      <a-image :width="100" src="/images/footer/websiteQrcode.jpg" :preview="false" />
                      <p class="text-white">视频号</p>
                    </a-col>
                  </a-row>
                </a-col>
              </a-row>
            </div>
          </a-col>
        </a-row>
      </div>

      <div class="copyright">
        <div class="container">
          <div class="row">
            <div class="col-12">
              <div class="copyright-inner">
                <div class="text-center">
                  <!-- <p class="copyright-text d-none d-lg-block">
                    珠海横琴指数动力科技有限公司 粤ICP备2022023386号-2
                  </p> -->
                  <p class="copyright-text d-lg-none">珠海横琴指数动力科技有限公司</p>

                  <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">
                    <p class="copyright-text d-lg-none">粤ICP备2022023386号</p>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- </footer> -->
    </div>
  </a-layout-footer>
</template>

<script lang="ts">
export default { name: 'SiteFooter' }
</script>

<script setup lang="ts">
import iconfontIcon from '~/components/tools/iconfontIcon.vue'
import { useEventBus } from '@vueuse/core'
import { useRoute } from 'vue-router'

const bus = useEventBus('showModal')
const route = useRoute()
</script>

<style scoped lang="less">
.siteFooter {
  background-color: #040837;
  padding: 0;
  .footerBanner {
    width: 100%;
    // height: 90px;
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url('/images/footer/footerBg.png');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50% 50%;
    @media screen and (-webkit-min-device-pixel-ratio: 2) {
      background-image: url('/images/footer/<EMAIL>');
    }

    .footerBanner__text {
      display: inline-block;
      font-size: 18px;
      color: #ffffff;
      margin-right: 32px;
    }
  }

  .copyright {
    margin-top: 32px;
    .copyright-inner {
      border-top: 1px dashed rgba(255, 255, 255, 0.2);
      padding: 32px 0px;
    }
    .copyright-text {
      color: #898f9e;
      font-size: 14px;
    }

    .copyright-text a {
      color: #5851a2;
    }
  }

  .footer-area {
    background: #040837;
    position: relative;
  }
  .footer-top-inner {
    padding: 50px 0px 80px;
  }
  .single-widget {
    margin-top: 30px;
    .widget-title {
      color: #fff;
      font-weight: 700;
      font-size: 25px;
      text-transform: capitalize;
      margin-bottom: 25px;
      position: relative;
      padding-bottom: 20px;
      &::before {
        position: absolute;
        content: '';
        width: 150px;
        height: 3px;
        background: #fff;
        bottom: 0;
        opacity: 0.1;
        left: 0;
        -webkit-transition: all 0.4s ease;
        -moz-transition: all 0.4s ease;
        transition: all 0.4s ease;
      }
      &::after {
        position: absolute;
        content: '';
        width: 50px;
        height: 3px;
        background: #5851a2;
        bottom: 0;
        opacity: 1;
        left: 0;
        -webkit-transition: all 0.4s ease;
        -moz-transition: all 0.4s ease;
        transition: all 0.4s ease;
      }
    }
    &:hover {
      .widget-title {
        &::after {
          width: 150px;
        }
      }
    }
    li {
      margin-bottom: 15px;
      a {
        color: #fff;
        font-weight: 400;
        font-size: 16px;
      }
    }
  }
  .widget-footer-info {
    a {
      display: block;
      font-size: 16px;
      color: #fff;
      margin-top: 10px;
      transition: all 0.5s ease-in-out;
      font-weight: 400;
      &:hover {
        color: #5851a2;
        padding-left: 10px;
      }
      i {
        margin-right: 5px;
      }
    }
  }
  .widget-content {
    display: flex;
    align-items: flex-start;
    ul {
      list-style: none;
    }
  }
  .f-contact-inner {
    li {
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 20px;
      position: relative;
      padding-left: 60px;
      .iconfontIcon {
        position: absolute;
        content: '';
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #fff;
        border-radius: 100%;
        color: #5851a2;
        font-size: 18px;
        line-height: 40px;
        text-align: center;
        -webkit-transition: all 0.4s ease;
        -moz-transition: all 0.4s ease;
        transition: all 0.4s ease;
        top: 6px;
      }
      &:hover {
        i {
          background: #5851a2;
          color: #fff;
        }
      }
    }
  }
  .f-contact-qrCode {
    text-align: center;
    img {
      max-width: 100px;
      width: 90%;
    }
    p {
      color: #fff;
      font-size: 14px;
      font-weight: 400;
    }
  }
}
</style>
