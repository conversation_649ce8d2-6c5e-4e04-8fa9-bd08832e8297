<template>
  <!-- 如果没有子菜单，渲染普通菜单项 -->
  <a-menu-item v-if="isEmpty(routerItem.children)" :key="routerItem.path">
    <NuxtLink :to="{ path: routerItem.path }">
      {{ routerItem.meta?.title }}
    </NuxtLink>
    <!-- <a :href="routerItem.path">{{ routerItem.meta?.title }}</a> -->
  </a-menu-item>
  
  <!-- 如果有子菜单 -->
  <template v-else>
    <!-- 水平模式 -->
    <a-sub-menu 
      v-if="mode === 'horizontal'" 
      :key="routerItem.path" 
      :title="routerItem.meta?.title"
    >
      <template v-for="childrenRouter in routerItem.children" :key="childrenRouter.path">
        <MenuItemRecursive :router-item="childrenRouter" :mode="mode" />
      </template>
    </a-sub-menu>
    
    <!-- 内联模式 -->
    <ClientOnly v-else>
      <a-sub-menu 
        :key="routerItem.path" 
        :title="routerItem.meta?.title"
      >
        <template v-for="childrenRouter in routerItem.children" :key="childrenRouter.path">
          <MenuItemRecursive :router-item="childrenRouter" :mode="mode" />
        </template>
      </a-sub-menu>
    </ClientOnly>
  </template>
</template>

<script setup lang="ts">
import { type RouteRecordRaw } from 'vue-router'
import { isEmpty } from 'lodash-es'

interface Props {
  routerItem: RouteRecordRaw
  mode: 'horizontal' | 'inline'
}

defineProps<Props>()
</script>
