<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:46:54
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 10:33:59
 * @FilePath: /busienginewebsite/components/layout/siteHeader/index.vue
 * @Description: 
-->
<template>
  <a-layout-header :class="['siteHeader', isShowList ? 'h-100' : '']">
    <a-row class="container menuHeight">
      <a-col :xs="{ span: 8 }" :sm="{ span: 8 }" :lg="{ span: 5 }">
        <!-- class="menuHeight" -->
        <router-link to="/" class="logo">
          <img src="/images/common/logo1.svg" alt="#" />
        </router-link>
      </a-col>

      <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :lg="{ span: 15 }">
        <menu-item v-model:value="selectedKeys"></menu-item>
      </a-col>

      <a-col :xs="{ span: 16 }" :sm="{ span: 16 }" :lg="{ span: 0 }">
        <div class="text-end">
          <a class="hoverPrimaryColor" @click="isShowList = !isShowList">
            <iconfontIcon v-if="!isShowList" icon="icon-view-list" class="fw-bold fs-2" />
            <iconfontIcon v-else icon="icon-close" class="fw-bold fs-2" />
          </a>
        </div>
      </a-col>

      <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :lg="{ span: 4 }">
        <a-space class="text-end demonstrateBtn">
          <template v-if="route.path === '/becomingPartners'">
            <a-button type="default" size="large" class="fw-bold" @click="bus.emit('showModal')"> 申请合作 </a-button>
          </template>
          <template v-else>
            <a-button
              type="default"
              size="large"
              :style="{ paddingLeft: '21px' }"
              class="fw-bold"
              target="_blank"
              href="//gaoguanshuo.com"
            >
              个人版
              <iconfontIcon
                icon="icon-caret-right-small"
                :extraCommonProps="{ style: { fontSize: '16px', margin: 0, color: '#a6a6a6' } }"
              />
            </a-button>
            <a-button
              type="default"
              size="large"
              :style="{ paddingLeft: '21px' }"
              class="fw-bold"
              target="_blank"
              href="https://eia.bengine.com.cn/"
            >
              企业版
              <iconfontIcon
                icon="icon-caret-right-small"
                :extraCommonProps="{ style: { fontSize: '16px', margin: 0, color: '#a6a6a6' } }"
              />
            </a-button>
          </template>
        </a-space>
      </a-col>
    </a-row>

    <div class="treeMenu">
      <div class="container">
        <menu-item v-model:value="selectedKeys" class="menuHeight" mode="inline"></menu-item>
      </div>
    </div>
  </a-layout-header>
</template>

<script lang="ts">
export default { name: 'SiteHeader' }
</script>

<script setup lang="ts">
import iconfontIcon from '~/components/tools/iconfontIcon.vue'
import { nextTick, onMounted, ref, watch } from 'vue'
import { RouterLink, useRoute } from 'vue-router'
import { useEventBus } from '@vueuse/core'
import menuItem from './menuItem.vue'
const bus = useEventBus('showModal')
const isShowList = ref(false)

const route = useRoute()
const selectedKeys = ref<string[]>()
watch(
  () => route.path,
  (newPath, oldPath) => {
    isShowList.value = false
    selectedKeys.value = [route.path]
  },
  {
    immediate: true,
  }
)

onMounted(async () => {
  window.onresize = () => {
    if (document.body.clientWidth >= 992) {
      isShowList.value = false
    }
  }
})
</script>

<style scoped lang="less">
@import '@/assets/less/media.less';

.h-100 {
  height: 100% !important;
}
.siteHeader {
  transition: height 0.3s;
  z-index: 99;
  overflow: hidden;
  background-color: #fff;
  height: 68px;
  padding: 0;
  .menuHeight {
    height: var(--nav-menu__height);
    line-height: var(--nav-menu__height);
  }

  .logo {
    display: block;
    // align-items: center;
    // justify-content: center;
    img {
      /* prettier-ignore */
      .media-query(xs,{max-width: 180Px;});
      /* prettier-ignore */
      .media-query(sm,{max-width: 180Px;});
      /* prettier-ignore */
      .media-query(md,{max-width: 100%;});
      object-fit: contain;
      // height: 100%;

      /* prettier-ignore */
      // height: calc(var(--nav-menu__height) - 12Px);
    }
  }

  .treeMenu {
    transition: all 0.3s;
    .ant-menu {
      border: none;
    }
  }

  .demonstrateBtn {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: right;
  }
}
</style>
