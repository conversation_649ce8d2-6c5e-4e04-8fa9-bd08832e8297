<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-27 10:32:45
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 10:38:16
 * @FilePath: /busienginewebsite/components/layout/siteHeader/menuItem.vue
 * @Description: 
-->
<template>
  <a-menu :selectedKeys="selectedKeys" :mode="mode">
    <template v-for="routerItem in baseRouter" :key="routerItem.path">
      <MenuItemRecursive :router-item="routerItem" :mode="mode" />
    </template>
  </a-menu>
</template>

<script setup lang="ts">
import { type RouteRecordRaw } from 'vue-router'
import { baseRouter } from '../../../app/router.options'
import MenuItemRecursive from './MenuItemRecursive.vue'
import { toRef } from 'vue'

interface Props {
  value?: string[]
  mode?: 'horizontal' | 'inline'
}

const props = withDefaults(defineProps<Props>(), {
  value: () => ['/'],
  mode: 'horizontal',
})

const selectedKeys = toRef(props, 'value')
const mode = toRef(props, 'mode')
</script>
