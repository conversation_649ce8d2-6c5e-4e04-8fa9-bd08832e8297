<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-08-16 19:08:13
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-08-18 17:57:09
 * @FilePath: /official-website/src/components/rowWrap/index.vue
 * @Description: 
-->
<template>
  <div class="rowWrap">
    <a-row :gutter="props.gutter" :class="props.isReverse ? 'isReverse' : ''">
      <a-col v-bind="leftColConfig">
        <slot name="leftBox"></slot>
      </a-col>
      <a-col v-bind="rightColConfig">
        <slot name="rightBox"></slot>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
export default { name: 'rowWrap' }
</script>

<script setup lang="ts">
import { computed, ref } from 'vue'

const props = defineProps({
  isReverse: { type: Boolean, default: false },
  leftCol: { type: Object, default: () => {} },
  rightCol: { type: Object, default: () => {} },
  gutter: {
    type: Array<Number>,
    default: () => [64, 32]
  }
})

const leftColConfig = computed(() => {
  return Object.assign(
    {
      xs: { span: 24 },
      sm: { span: 24 },
      md: { span: 10 }
    },
    props.leftCol
  )
})

const rightColConfig = computed(() => {
  return Object.assign(
    {
      xs: { span: 24 },
      sm: { span: 24 },
      md: { span: 14 }
    },
    props.rightCol
  )
})
</script>

<style lang="less" scoped>
.rowWrap {
  .isReverse {
    flex-direction: row-reverse;
  }
}
</style>
