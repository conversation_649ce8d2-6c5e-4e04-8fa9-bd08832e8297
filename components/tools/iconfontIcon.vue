<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-26 16:41:17
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 10:30:34
 * @FilePath: /busienginewebsite/components/tools/iconfontIcon.vue
 * @Description: 
-->
<template>
  <IconFont :type="props.icon" :style="{ fontSize: '20px' }" class="iconfontIcon" v-bind="props.extraCommonProps" />
</template>

<script setup lang="ts">
import { createFromIconfontCN } from '@ant-design/icons-vue'

const IconFont = createFromIconfontCN({
  scriptUrl: [
    '//at.alicdn.com/t/c/font_3584494_icsn9etbq9.js',
    '//at.alicdn.com/t/c/font_4162393_66ypsbx456b.js',
    '//at.alicdn.com/t/c/font_3116454_jz913c002mq.js',
  ],
})

const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  extraCommonProps: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style scoped></style>
