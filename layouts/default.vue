<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:12:28
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-02 17:01:33
 * @FilePath: /busienginewebsite-ssr/layouts/default.vue
 * @Description: 
-->
<template>
  <a-config-provider :locale="locale" :theme="{ token: { colorPrimary: '#5851a2' } }">
    <a-layout>
      <siteHeader />
      <a-layout-content class="contentContainer">
        <slot />
        <div class="call-me fixed-btn fw-bold">
          <p @click="bus.emit('showModal')">{{ route.path === '/becomingPartners' ? '申请合作' : '申请体验' }}</p>
        </div>
      </a-layout-content>
      <siteFooter />

      <a-back-top />

      <a-modal v-model:open="visible" :footer="null" @cancel="handlerClose">
        <div class="seekAdviceFromBox" v-if="!showResult">
          <div class="tips">
            <h3>{{ route.path === '/becomingPartners' ? '申请成为合作伙伴' : '获得更多专业咨询' }}</h3>
            <!-- <p>感谢关注指数动力！请完整填写以下表格，以便我们能够联系到您，为您提供专业的咨询与服务。</p> -->
          </div>
          <a-form ref="formRefs" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
            <a-form-item label="您的姓名" name="name">
              <a-input placeholder="请输入姓名" v-model:value="formData.name"></a-input>
            </a-form-item>
            <a-form-item label="手机号码" name="phone">
              <a-input placeholder="请输入手机号码" v-model:value="formData.phone"></a-input>
            </a-form-item>
            <a-form-item label="公司名称" name="companyName">
              <a-input placeholder="请输入公司名称" v-model:value="formData.companyName"></a-input>
            </a-form-item>
            <a-form-item label="您的职务" name="duties">
              <a-input placeholder="请输入职务" v-model:value="formData.duties"></a-input>
            </a-form-item>
            <a-form-item :wrapper-col="{ xs: { offset: 0 }, sm: { offset: 8 }, md: { offset: 8 } }">
              <a-col :xs="{ span: 24 }" :sm="{ span: 0 }">
                <a-button type="primary" style="width: 100%" @click="submit"> 提交 </a-button>
              </a-col>
              <a-col :xs="{ span: 0 }" :sm="{ span: 24 }">
                <a-button type="primary" style="width: 160px" @click="submit"> 提交 </a-button>
              </a-col>
            </a-form-item>
          </a-form>
          <!-- <iframe src="https://www.wjx.top/vj/QRFSVqz.aspx" frameborder="0"></iframe> -->
        </div>

        <template v-else>
          <a-result status="success" title="提交成功！" sub-title="稍后将会有工作人员与你联系">
            <template #extra>
              <!-- <a-button key="console" type="primary"></a-button> -->
              <a-button @click="handlerClose">关闭</a-button>
            </template>
          </a-result>
        </template>
      </a-modal>
    </a-layout>
  </a-config-provider>
</template>

<script setup lang="ts">
import { LayoutSiteHeader as siteHeader, LayoutSiteFooter as siteFooter } from '#components'
import type { RuleObject } from 'ant-design-vue/es/form'

import locale from 'ant-design-vue/es/locale/zh_CN'

const bus = useEventBus('showModal')

const visible = ref(false)
bus.on((e) => {
  console.log('e: ', e)
  visible.value = !visible.value
})

const loading = ref(false)
const showResult = ref(false)
const rules: Record<string, RuleObject | RuleObject[]> = {
  companyName: [{ type: 'string', required: true, message: '请输入公司名称' }],
  name: [{ type: 'string', required: true, message: '请输入姓名' }],
  phone: [
    { type: 'string', required: true, message: '请输入手机号码' },
    { type: 'string', message: '请输入正确手机号码', pattern: /^(?:(?:\+|00)86)?1\d{10}$/ },
  ],
  duties: [{ type: 'string', required: true, message: '请输入职务' }],
}
const formData = ref({
  companyName: undefined,
  name: undefined,
  phone: undefined,
  duties: undefined,
})
const formRefs = ref()
const route = useRoute()

async function submit() {
  loading.value = true
  try {
    const validate = await formRefs.value.validate()
    console.log('validate: ', validate)
    loading.value = false
    // axios({
    //   url: 'https://service-52ximlnm-1317917275.gz.apigw.tencentcs.com/release/sendMsg',
    //   method: 'get',
    //   params: {
    //     name: formData.value.name,
    //     phone: formData.value.phone,
    //     companyName: formData.value.companyName,
    //     position: formData.value.duties,
    //     type: route.path === '/becomingPartners' ? '1' : '2',
    //   },
    // })
    const { data, error, refresh } = await useFetch('https://service-52ximlnm-1317917275.gz.apigw.tencentcs.com/release/sendMsg', {
      method: 'get',
      query: {
        name: formData.value.name,
        phone: formData.value.phone,
        companyName: formData.value.companyName,
        position: formData.value.duties,
        type: route.path === '/becomingPartners' ? '1' : '2',
      },
    })
    showResult.value = true
  } catch (error) {
    loading.value = false
    showResult.value = false
    console.error(error)
  }
}

function handlerClose() {
  console.log('refresh')
  // formRefs.value.resetFields()
  formData.value = {
    companyName: undefined,
    name: undefined,
    phone: undefined,
    duties: undefined,
  }
  showResult.value = false
  loading.value = false
  visible.value = false
}
</script>

<style lang="less" scoped>
.contentContainer {
  padding-top: var(--nav-menu__height);
}

.fixed-btn {
  position: fixed;
  top: calc(50% - 50px);
  right: 0;
  /* prettier-ignore */
  border-radius: 10Px 0 0 10Px;
  /* prettier-ignore */
  padding: 20Px 10Px;
  -webkit-writing-mode: vertical-rl;
  writing-mode: vertical-rl;
  z-index: 9999;
  cursor: pointer;
  /* prettier-ignore */
  font-size: 18Px;
  p {
    color: #fff !important;
  }
}

.call-me {
  background-color: #21becf;
  color: #fff;
  &:hover {
    background-color: #54e8f8;
  }
}

.seekAdviceFromBox {
  // height: 70vh;
  // width: 100%;
  // iframe {
  //   width: 100%;
  //   height: 100%;
  // }
  .tips {
    line-height: 1.5rem;
    color: rgb(24, 28, 37);
    letter-spacing: 0rem;
    h3 {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 16px;
    }
    p {
      font-size: 14px;
      margin-bottom: 32px;
    }
  }
}
</style>
