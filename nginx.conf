server {
  listen 80;
  # # #开启和关闭gzip模式
  # gzip on;
  # # #gizp压缩起点，文件大于10k才进行压缩
  # gzip_min_length 10k;
  # # # 配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
  # gzip_disable "MSIE [1-6]\.";
  # # # 设置压缩所需要的缓冲区大小，以4k为单位，如果文件为7k则申请2*4k的缓冲区
  # gzip_buffers 2 4k;
  # # # 设置gzip压缩针对的HTTP协议版本
  # gzip_http_version 1.1;
  # # # gzip 压缩级别，1-9，数字越大压缩的越好，也越占用CPU时间
  # gzip_comp_level 6;
  # # # 进行压缩的文件类型。
  # gzip_types application/javascript text/css;
  # # #nginx对于静态文件的处理模块，开启后会寻找以.gz结尾的文件，直接返回，不会占用cpu进行压缩，如果找不到则不进行压缩
  # gzip_static on;
  # # # 是否在http header中添加Vary: Accept-Encoding，建议开启
  # gzip_vary on;

  # server_name  localhost;
  # gzip_static on;
  gzip on;
  gzip_static on;
  gzip_comp_level 2;
  gzip_proxied any;
  gzip_types text/plain text/css application/x-javascript text/xml application/xml application/xml+rss text/javascript;
  underscores_in_headers on;
  client_max_body_size 20m;

  root /usr/share/nginx/html;
  index index.html index.htm;

  location / {
    try_files $uri $uri/ =404;
  }

  error_page 404 /404.html;
  location = /404.html {
    internal;
  }

  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
}
