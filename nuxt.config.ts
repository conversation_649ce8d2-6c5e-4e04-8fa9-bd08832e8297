/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-08-09 16:56:21
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 14:26:11
 * @FilePath: /busienginewebsite/nuxt.config.ts
 * @Description:
 */
import { defineNuxtConfig } from 'nuxt/config'
const { fileURLToPath } = require('node:url')

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  alias: {
    '@comp': fileURLToPath(new URL('./components', import.meta.url)),
  },
  antd: {
    extractStyle: true,
  },
  compatibilityDate: '2024-04-03',
  devServer: {
    port: 3001,
  },
  nitro: {
    devProxy: {
      '/corp_elf': {
        target: 'https://insight.bengine.com.cn/corp_elf/context/corpElf',
        changeOrigin: true,
      },
    },
  },
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },
  app: {
    buildAssetsDir: 'dist',
    head: {
      title: '指数动力官网-实时智能B2B市场竞争情报服务商',
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1' },
        { name: 'keywords', content: '指数动力,商瞳,智能营销,B2B市场洞察,ToB市场洞察,市场营销,市场洞察,GTM,营销策略' },
        {
          name: 'description',
          content:
            '指数动力科技有限公司是一家价值型B2B智能营销应用提供商，旗下产品「商瞳」能够帮助企业解决实时行业洞察与获客问题，产品致⼒于通过AI技术为企业提供精准行业分类、定制行业标签模型、筛选高价值有效潜客、API级信息补全服务等，助力企业高效获客。⾃成⽴以来，指数动力深耕B2B市场营销业务场景，不断提升自然语言、图像识别和知识图谱等AI技术能力，构建亿级企业数据库及行业知识库。',
        },
      ],
    },
  },
  css: ['ant-design-vue/dist/reset.css', 'animate.css', '~/assets/less/main.less'],
  modules: [
    // '@nuxtjs/eslint-module',
    '@vueuse/nuxt',
    'nuxt-lodash',
    '@ant-design-vue/nuxt',
    '@unocss/nuxt',
  ],
  vite: {
    resolve: {
      alias: {
        'ant-design-vue/dist': 'ant-design-vue/dist',
        'ant-design-vue/es': 'ant-design-vue/es',
        'ant-design-vue/lib': 'ant-design-vue/es',
        'ant-design-vue': 'ant-design-vue/es',
      },
    },
  },
  postcss: {
    plugins: {
      'postcss-px-to-viewport-8-plugin': {
        unitToConvert: 'px', // 要转化的单位
        viewportWidth: 1920, // UI设计稿的宽度
        unitPrecision: 6, // 转换后的精度，即小数点位数
        propList: ['*', '!font*'], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
        viewportUnit: 'vw', // 指定需要转换成的视窗单位，默认vw
        fontViewportUnit: 'vw', // 指定字体需要转换成的视窗单位，默认vw
        selectorBlackList: ['ant', 'container', 'siteFooter', 'siteHeader'], // 指定不转换为视窗单位的类名，
        minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
        mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
        replace: true, // 是否转换后直接更换属性值
        // exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
        exclude: [],
        landscape: false, // 是否处理横屏情况
      },
    },
  },
})
