{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@ant-design-vue/nuxt": "^1.4.6", "@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^11.0.1", "@vueuse/nuxt": "^11.0.1", "animate.css": "^4.1.1", "ant-design-vue": "^4.2.6", "jquery": "^3.7.1", "less": "^4.2.0", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "nuxt": "^3.17.5", "vue": "^3.5.17"}, "devDependencies": {"@nuxtjs/eslint-module": "^4.1.0", "@types/fs-extra": "^11.0.4", "@types/jquery": "^3.5.30", "@types/lodash-es": "^4.17.12", "@unocss/nuxt": "^66.3.2", "eslint": "^9.9.0", "fs-extra": "^11.2.0", "nuxt-lodash": "^2.5.3", "postcss-px-to-viewport-8-plugin": "^1.2.5", "unocss": "^66.3.2"}}