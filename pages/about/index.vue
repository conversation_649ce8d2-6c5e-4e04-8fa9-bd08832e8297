<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-02 15:36:37
 * @FilePath: /busienginewebsite-ssr/pages/about/index.vue
 * @Description: 
-->
<template>
  <div class="about">
    <div class="banner">
      <img
        src="/images/common/solutionBannerBg.png"
        srcset="/images/common/solutionBannerBg.png 1x, /images/common/<EMAIL> 2x"
        class="bannerBg"
      />
      <div class="container">
        <h1 class="title">市场洞察和智能营销服务商</h1>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container linkage">
        <rowWrap
          class="aboutWarp"
          :isReverse="false"
          :gutter="[32, 32]"
          :leftCol="{
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 24 },
            lg: { span: 12 },
          }"
          :rightCol="{
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 24 },
            lg: { span: 12 },
          }"
        >
          <template #leftBox>
            <div class="leftBox">
              <!-- <div class="title">三方联动，实现资源的最优利用</div> -->
              <div class="desc">
                <p>指数动力成立于2021年，总部设立在国家级新区珠海横琴，在北京、上海、广州设有业务代表处。</p>
                <br />
                <p>
                  指数动力拥有一支具备深耕B2B企业SaaS服务及大数据领域的专业团队，核心人员来自IBM、埃森哲、Oracle、SAP、阿里、腾讯、金山等500强知名企业，拥有丰富的产品、研发、算法及运营实战经验。构建了从数据收集、治理、建模到B2B市场洞察及智能营销解决方案。
                </p>
                <br />
                <p>
                  目前，指数动力已为腾讯、华为、阿里、美的、宁德时代等全国50家行业头部企业提供服务，致力于企业及政府实现业务变革和营收增长。
                </p>
                <br />
                <p>2022年腾讯云授予指数动力“金牌数据服务商”。</p>
              </div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img src="/images/about/img1.jpg" />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
  </div>
</template>

<script>
export default { name: 'AboutIndex' }
</script>

<script setup>
import rowWrap from '@comp/rowWrap/index.vue'
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';

.banner {
  width: 100%;
  /* prettier-ignore */
  height: 262Px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  .bannerBg {
    display: block;
    // max-width: 1320px;
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    // top: 0;
    // left: 0;
    // bottom: 0;
    // right: 0;
  }

  .container {
    z-index: 9;
    position: relative;

    .title {
      color: #000000;
      font-size: 60px;
      font-weight: 600;
      letter-spacing: 0;
      text-align: center;

      strong {
        color: #59479a;
      }

      .media-query(xs,{font-size: 32px;});
      .media-query(sm,{font-size: 32px;});
      .media-query(lg,{font-size: 36px;});
      .media-query(xl,{font-size: 44px;});
    }
  }
}

.aboutWarp {
  /* prettier-ignore */
  padding: 64Px 0;
  .leftBox {
    height: 100%;
    max-width: 480px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .media-query(xs,{ max-width: 100%; text-align: center; });
    .media-query(sm,{ max-width: 100%; text-align: center; });
    .media-query(md,{ text-align: left; });

    .title {
      font-weight: 600;
      font-size: 36px;
      color: #333333;
    }
    .desc {
      font-weight: 400;
      font-size: 18px;
      color: #555555;
      line-height: 2;
      /* prettier-ignore */
      margin-top: 30Px;
    }
  }
  .rightBox {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    @media (max-width: 992px) {
      justify-content: center !important;
    }
    img {
      width: 100%;
      /* prettier-ignore */
      max-height: 520Px;
      object-fit: contain;
    }
  }
}
</style>
