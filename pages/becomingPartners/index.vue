<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-09-11 11:34:35
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-02 15:42:49
 * @FilePath: /busienginewebsite-ssr/pages/becomingPartners/index.vue
 * @Description: 成为伙伴
-->

<template>
  <div class="becomingPartners">
    <div class="banner">
      <img
        src="/images/common/solutionBannerBg.png"
        class="bannerBg"
        srcset="/images/common/solutionBannerBg.png 1x, /images/common/<EMAIL> 2x"
      />
      <div class="container">
        <h1 class="title">合作伙伴招募及激励计划</h1>
        <div class="text-center">
          <a-button size="large" type="primary" shape="round" @click="bus.emit('showModal')">申请合作</a-button>
        </div>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container beneficial">
        <div class="header">
          <div class="headerTitle">成为指数动力伙伴的三大利好</div>
        </div>

        <a-row :gutter="[16, 16]">
          <a-col v-for="(item, index) in beneficialList" :key="index" :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 8 }">
            <div class="beneficial_Item">
              <img
                class="beneficial_Item_Img"
                :src="`/images/product/eia/marketingGrowth${index + 1}.png`"
                :srcset="`
                ${`/images/product/eia/marketingGrowth${index + 1}.png`} 1x, 
                ${`/images/product/eia/marketingGrowth${index + 1}@2x.png`} 2x
                `"
              />
              <p class="beneficial_Item_Title">{{ item.title }}</p>
              <div class="beneficial_Item_Desc">{{ item.desc }}</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container deliveryCapability">
        <div class="header">
          <div class="headerTitle">我们拥有强大的产品研发实力和项目交付能力</div>
        </div>

        <a-row :gutter="[16, 16]">
          <a-col v-for="(item, index) in deliveryCapabilityList" :key="index" :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 8 }">
            <div class="deliveryCapability_Item">
              <div class="deliveryCapability_Item_Bg">
                <div class="deliveryCapability_Item_Title">
                  <img
                    class="deliveryCapability_Item_Img"
                    :src="`${item.imgUrl}.png`"
                    :srcset="`
                      ${`${item.imgUrl}.png`} 1x, 
                      ${`${item.imgUrl}@2x.png`} 2x
                    `"
                  />
                  <p>{{ item.title }}</p>
                </div>
                <div class="deliveryCapability_Item_Desc">{{ item.desc }}</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- <div class="container-bg1">
      <div class="container researchAndDevelopment">
        <div class="header">
          <div class="headerTitle">我们拥有强大的产品研发实力</div>
        </div>

        <a-row :gutter="[16, 16]">
          <a-col v-for="(item, index) in researchAndDevelopmentList" :key="index" :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 8 }">
            <div class="researchAndDevelopment_Item">
              <img
                class="researchAndDevelopment_Item_Img"
                :src="getImageUrl(`images/product/eia/marketingGrowth${index + 1}.png`)"
                :srcset="`
                ${getImageUrl(`images/product/eia/marketingGrowth${index + 1}.png`)} 1x, 
                ${getImageUrl(`images/product/eia/marketingGrowth${index + 1}@2x.png`)} 2x
                `"
              />
              <p class="researchAndDevelopment_Item_Title">{{ item.title }}</p>
              <div class="researchAndDevelopment_Item_Desc">{{ item.desc }}</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div> -->

    <div class="container-bg1">
      <div class="container support">
        <div class="header">
          <div class="headerTitle">我们拥有完善的合作伙伴扶持体系</div>
        </div>

        <div></div>

        <a-row :gutter="[16, 16]">
          <a-col v-for="(item, index) in supportList" :key="index" :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 8 }">
            <div class="support_Item">
              <img :src="`/images/becomingPartners/supportList${index + 1}.png`" />
              <p class="support_Item_Title">{{ item.title }}</p>
              <div class="support_Item_Desc">{{ item.desc }}</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container invite">
        <div class="header">
          <div class="headerTitle">我们诚邀两类合作伙伴</div>
          <div class="headerDesc">组织合作伙伴 & 个人合作伙伴</div>
        </div>

        <rowWrap :isReverse="true">
          <template #leftBox>
            <div class="leftBox">
              <!-- <div class="title"></div> -->
              <ul>
                <li class="desc">有一定的中大型B2B客户资源,了解中大型B2B企业客户需求,并能帮助客户选择最佳的产品和解决方案</li>
                <li class="desc">深耕特定垂直行业,熟知行业客户需求,协助完成垂直行业解决方案并使用商曈平台助力行业客户快速发展</li>
              </ul>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/solution/business/linkage.png`"
                  :srcset="`
                    ${`/images/solution/business/linkage.png`} 1x, 
                    ${`/images/solution/business/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container step">
        <div class="header">
          <div class="headerTitle">轻松四步，快速成为我们的伙伴</div>
        </div>

        <a-row :gutter="[16, 16]">
          <!-- 除sm屏幕大小外使用 -->
          <a-col v-for="(item, index) in stepList" :key="index" :xs="{ span: 24 }" :sm="{ span: 0 }" :md="{ span: 6 }">
            <div class="step_Item">
              <div class="step_Item_Content">
                <img class="step_Item_Content_Img" :src="item.imgUrl" />
                <p class="step_Item_Content_Title">{{ item.title }}</p>
                <div class="step_Item_Content_Desc">{{ item.desc }}</div>
                <!-- background-image: url('@/assets/'); -->
              </div>
              <!-- <span :class="[index !== 3 ? 'step_Item_Arrow' : '']"></span> -->

              <template v-if="index !== 3">
                <img class="step_Item_Arrow" :src="`/images/becomingPartners/arrow.png`" />
              </template>
            </div>
          </a-col>
          <!-- sm屏幕大小下使用 -->
          <a-col v-for="(item, index) in tepList_sm" :key="index" :xs="{ span: 0 }" :sm="{ span: 12 }" :md="{ span: 0 }">
            <div :class="['step_Item', `sm_step_Item${index + 1}`]">
              <div class="step_Item_Content">
                <img class="step_Item_Content_Img" :src="item.imgUrl" />
                <p class="step_Item_Content_Title">{{ item.title }}</p>
                <div class="step_Item_Content_Desc">{{ item.desc }}</div>
                <!-- background-image: url('@/assets/'); -->
              </div>
              <!-- <span :class="[index !== 3 ? 'step_Item_Arrow' : '']"></span> -->

              <template v-if="index !== 3">
                <img class="step_Item_Arrow" :src="`/images/becomingPartners/arrow.png`" />
              </template>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useEventBus } from '@vueuse/core'
const bus = useEventBus('showModal')

const beneficialList = [
  { title: '共创生意', desc: '拥有优秀的行业标杆客户，为你提供丰富的生意场景' },
  { title: '收益理想', desc: '项目合作方式灵活性高，收益理想。SaaS合作方式持续性高，收益连续' },
  { title: '交付无忧', desc: '项目合作交付工作负责，无后顾之忧。SaaS合作交付及服务全程支持，快速验收' },
]
//

const deliveryCapabilityList = [
  {
    title: '百万级项目交付经验',
    desc: '团队拥有成熟的项目管理和执行经验，有能力应对大规模和高价值的项目，拥有高效的资源管理、风险管理和协作技能，可以确保项目按时保质交付。',
    imgUrl: '/images/home/<USER>',
  },
  {
    title: '头部企业成功交付经验',
    desc: '团队积累了与全球领先企业和顶级公司的成功合作经验，以确保我们拥有高度的专业知识和卓越的能力，能够满足行业最高的标准和要求。',
    imgUrl: '/images/product/eia/marketingGrowth3',
  },
  {
    title: '行业深度理解+专业技能',
    desc: '团队拥有深入了解行业的专业知识和技能，能够为客户提供高度定制、高质量的解决方案，以满足行业的各种需求和要求。',
    imgUrl: '/images/home/<USER>',
  },
  {
    title: '多元解决方案组合',
    desc: '多种产品组合涵盖企业获客、触客和管理全流程提供满足客户差异化需求的解决方案。',
    imgUrl: '/images/product/eia/marketingGrowth2',
  },
  {
    title: '超快的产品迭代速度',
    desc: '跟随市场需求的变化，积极进行产品快速迭代和创新，以确保客户的业务能够持续顺利运营。',
    imgUrl: '/images/product/eia/marketingGrowth1',
  },
  {
    title: '专业的运营服务能力',
    desc: '以客户成功与发展为服务核心专业运营团队为数干家商户提供支持与指。',
    imgUrl: '/images/home/<USER>',
  },
]

const supportList = [
  { title: '完善的培训体系', desc: '不定期举行合作伙伴培训活动，对合作伙伴的全方位销售与客户服务进行指导' },
  { title: '专业的运营团队', desc: '资深专业运营团队，12*7随时为客户解决问题，提供帮助' },
  { title: '稳定的技术支持', desc: '强大的研发团队保障系统的安全性、稳定性、可靠性、让合作伙伴无后顾之忧' },
]
const stepList = [
  { title: '提交申请', desc: '', imgUrl: '/images/becomingPartners/step1.png' },
  { title: '合作洽谈', desc: '', imgUrl: '/images/becomingPartners/step2.png' },
  { title: '签约合作', desc: '', imgUrl: '/images/becomingPartners/step3.png' },
  { title: '互利共赢', desc: '', imgUrl: '/images/becomingPartners/step4.png' },
]
const tepList_sm = [
  { title: '提交申请', desc: '', imgUrl: '/images/becomingPartners/step1.png' },
  { title: '合作洽谈', desc: '', imgUrl: '/images/becomingPartners/step2.png' },
  { title: '互利共赢', desc: '', imgUrl: '/images/becomingPartners/step4.png' },
  { title: '签约合作', desc: '', imgUrl: '/images/becomingPartners/step3.png' },
]
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';

.becomingPartners {
  .banner {
    width: 100%;
    /* prettier-ignore */
    height: 262Px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: center;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }
    }
  }

  .beneficial,
  .deliveryCapability,
  .researchAndDevelopment,
  .support,
  .invite,
  .step {
    padding-bottom: 90px;
  }

  // 扶持、交付能力、研发
  .beneficial,
  .support {
    &_Item {
      /* prettier-ignore */
      max-width: 260Px;
      margin: 0 auto;
      &_Img {
        display: block;
        /* prettier-ignore */
        width: 54Px;
        /* prettier-ignore */
        height: 62Px;
        margin: 0 auto 22px;
      }
      &_Title {
        font-weight: 500;
        font-size: 24px;
        color: #5851a2;
        text-align: center;
        margin-bottom: 26px;
      }
      &_Desc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        text-align: center;
      }
    }
  }

  // 利好
  .beneficial {
  }
  // 交付能力
  .deliveryCapability {
    &_Item {
      /* prettier-ignore */
      max-width: 313Px;

      /* prettier-ignore */
      .media-query(md,{height: 410Px;});
      // background: #f7f8fb;
      border: 2px solid #ffffff;
      box-shadow: 0 13px 20px 0 #8b85a03b;
      /* prettier-ignore */
      border-radius: 8Px;
      padding: 10px;
      margin: 0 auto;

      &_Bg {
        background-color: #fff;
        /* prettier-ignore */
        border-radius: 8Px;
        height: 100%;
        /* prettier-ignore */
        padding: 16Px;
      }
      &_Title {
        font-weight: 600;
        font-size: 24px;
        padding-bottom: 12px;
        color: #502498;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #dddddd;

        img {
          /* prettier-ignore */
          width: 52Px;
          /* prettier-ignore */
          height: 58Px;
          margin-bottom: 16px;
        }
      }
      &_Desc {
        margin-top: 24px;
        // font-weight: 400;
        // font-size: 18px;
        color: #333333;
        line-height: 1.7;

        font-weight: 400;
        font-size: 18px;
        color: #333333;
        // line-height: 36px;
      }
    }
  }
  // 研发
  // .researchAndDevelopment {
  // }
  // 扶持
  .support {
    img {
      display: block;
      margin: 0 auto;
    }
  }
  // 邀请
  .invite {
    .leftBox {
      height: 100%;
      max-width: 480px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .media-query(xs,{ max-width: 100%; text-align: center; });
      .media-query(sm,{ max-width: 100%; text-align: center; });
      .media-query(md,{ text-align: left; });

      .title {
        font-weight: 550;
        font-size: 24px;
        color: #333333;
      }
      .desc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        line-height: 2;
        margin-bottom: 12px;
      }

      ul {
        list-style: circle;
      }
    }
    .rightBox {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      @media (max-width: 768px) {
        justify-content: center !important;
      }
      img {
        width: 100%;
        /* prettier-ignore */
        max-height: 520Px;
        object-fit: contain;
      }
    }
  }
  // 步骤
  .step {
    // .ant-col {
    //   &:nth-child(2) {
    //     .media-query(sm,{
    //       .step_Item {
    //         color: red

    //       //   display: flex;
    //       // flex-direction: column;
    //       // align-items: center;
    //       // .step_Arrow {
    //       //   position: relative;
    //       //   display: block;
    //       //   transform: rotate(90deg);
    //       //   /  prettier-ignore  /
    //       //   margin: 20Px 0;
    //       // }
    //       }
    //     });
    //   }
    // }

    &_Item {
      /* prettier-ignore */
      // max-width: 260Px;
      margin: 0 auto;
      position: relative;
      text-align: center;
      // display: flex;
      // align-items: center;
      // justify-content: center;

      .media-query(xs,{
        display: flex;
        flex-direction: column;
        align-items: center;
      });
      .media-query(sm,{
        display: flex;
        align-items: center;
        justify-content: center;
      });
      // .media-query(md,{
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      // });

      &_Arrow {
        /* prettier-ignore */
        width: 40Px;
        /* prettier-ignore */
        height: 40Px;
        object-fit: contain;
        position: absolute;
        /* prettier-ignore */
        right: -20Px;
        transform: rotate(0deg);

        .media-query(xs,{
          position: relative;
          display: block;
          transform:rotate(90deg);
          /* prettier-ignore */
          margin: 20Px 0;
          right: 0
        });
        // .media-query(sm,{

        // });
        // .media-query(md,{
        //   position: absolute;
        //   /* prettier-ignore */
        //   right: -20Px;
        //   transform:rotate(0deg);
        // });
      }

      &_Content {
        margin: 0 auto;
        &_Img {
          display: block;
          /* prettier-ignore */
          width: 70Px;
          /* prettier-ignore */
          height: 70Px;
          margin: 0 auto 22px;
        }
        &_Title {
          font-weight: 500;
          font-size: 24px;
          color: #5851a2;
          text-align: center;
        }
      }
    }

    // sm屏幕大小下使用的样式
    .sm_step_Item2 {
      color: red;
      display: block;
      .step_Item_Arrow {
        position: relative;
        display: block;
        transform: rotate(90deg);
        right: 0;
        /* prettier-ignore */
        margin: 40Px auto;
      }
    }
    .sm_step_Item3 {
      .step_Item_Arrow {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
