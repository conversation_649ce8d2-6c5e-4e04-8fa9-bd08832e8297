<template>
  <div class="case">
    <div class="banner">
      <img
        src="/images/common/solutionBannerBg.png"
        class="bannerBg"
        srcset="/images/common/solutionBannerBg.png 1x, /images/common/<EMAIL> 2x"
      />

      <div class="container">
        <h1 class="title">客户故事</h1>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container">
        <a-tabs centered v-model:activeKey="activeKey" @change="handlerActiveKeyChange">
          <a-tab-pane v-for="(caseItem, index) in caseList" :key="caseItem.targetId" :tab="caseItem.target" forceRender> </a-tab-pane>
        </a-tabs>

        <template v-for="(caseItem, index) in caseList" :key="index">
          <div v-for="(articleItem, articleIndex) in caseItem.articleList" :key="articleIndex" class="articleItem" :id="caseItem.targetId">
            <!-- <div class="itemBg"> -->
            <h1 class="title">{{ articleItem.title }}</h1>
            <p class="secondTitle">{{ articleItem.secondTitle }}</p>
            <div class="contextWarp">
              <div v-for="(descItem, descIndex) in articleItem.desc" :key="descIndex" class="warpItem">
                <div class="warpTitle">{{ descItem.title }}</div>
                <template v-if="caseItem.target === '无人零售' && descItem.title === '客户价值'">
                  <div class="warpImg">
                    <img src="/images/case/case1.jpg" alt="1212" />
                  </div>
                </template>
                <template v-else>
                  <div v-for="(contextItem, contextIndex) in descItem.context" :key="contextIndex">
                    {{ contextItem }}
                  </div>
                </template>
              </div>
            </div>
            <!-- </div> -->
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'CaseIndex',
}
</script>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { minBy, isEmpty } from 'lodash-es'
import { useWindowScroll } from '@vueuse/core'
import $ from 'jquery'
import type { Key } from 'ant-design-vue/es/_util/type'

const route = useRoute()

const activeKey = ref('cloudServices')

const caseList = [
  {
    target: '云服务',
    targetId: 'cloudServices',
    articleList: [
      {
        title: '洞察目标市场，精准匹配客户',
        secondTitle: '某头部云服务厂商',
        desc: [
          {
            title: '案例背景',
            context: [
              '国内某知名云计算品牌，面向全世界各个国家和地区的政府机构、企业组织和个人开发者，提供全球领先的云计算、大数据、人工智能等技术产品与服务，以卓越的科技能力打造丰富的行业解决方案，构建开放共赢的云端生态，推动产业互联网建设，助力各行各业实现数字化升级',
            ],
          },
          {
            title: '客户痛点',
            context: [
              '1、定靶：聚焦于大行业的前提下，哪些细分市场的用云量和客户量尤为突出？',
              '2、探查：目标企业客户现在是哪些厂商为其提供云服务？每年的云应用投入预算可能有多少？',
              '3、瞄准：目标企业客户的数智化建设现状如何？IT类岗位如何设定？近期的事件有哪些？',
            ],
          },
          {
            title: '解决方案',
            context: [
              '从数据深挖、模型算法、场景应用三个层面，构建市场洞察-客户监测体系，提升总部-区域-一线的协同和作战能力',
              '1、数据深挖：从多种有效渠道的原始数据中挖掘有价值的企业信息，如企业数智化建设情况等。',
              '2、模型算法：基于IT技术指标、信息化指标、网络建设指标、规模动态指标四大维度建立科学合理的云用量评估模型。',
              '3、场景应用：穿透式应用，细分市场用云量-企业用云量-企业全景价值信息。更直观了解洞察目标市场',
            ],
          },
          {
            title: '客户价值',
            context: [
              '1、总部：支持总部了解重点市场，制定科学的GTM策略',
              '2、区域：支持区域对不同客户群体资源进行合理分配',
              '3、一线：帮助一线获取有价值的客户信息，有效行动',
            ],
          },
        ],
      },
      {
        title: '监测竞对动态与行为',
        secondTitle: '某全球头部云服务厂商',
        desc: [
          {
            title: '案例背景',
            context: [
              '全球某知名云计算品牌，全球领先的云计算及人工智能科技公司，为200多个国家和地区的企业、开发者和政府机构提供服务。致力于以在线公共服务的方式，提供安全、可靠的计算和数据处理能力，让计算和人工智能成为普惠科技。',
            ],
          },
          {
            title: '客户痛点',
            context: [
              '1、竞对情报：了解竞对动态和行为是赢得市场的关键工作，但随着老对手的竞争激励加剧，新对手的不断涌现，获取高质量竞争数据的成本高和时效性慢是该企业市场部面临的重点挑战；',
              '2、竞争支持：如何把最新的竞争信息做好内部传递和推送，及时赋予营销组织有效的竞争性支持，包括竞对的活动、社媒、战略合作、客户、圈层合作、战力部署、地域布局等信息；',
            ],
          },
          {
            title: '解决方案',
            context: [
              '1、竞对活动监测：定向监测核心竞对的重大活动，如云栖、阿里云峰会、华为全联接大会、华为开发者大会、AWS中国峰会、腾讯云开发者大会等。',
              '2、竞对社媒监测：核心竞对在主流媒体（官网、微博、微信、新闻网站等）的声量热度监测，包括品牌热度监测、官媒热度监测等。',
              '3、竞对客户监测：了解竞对在各个地区和行业下的客户名录，并对客户进行价值评估与分级，为一线做替换营销提供竞争支持。',
              '4、核心圈层监测：针对核心圈层组织进行最新的动态、政策及观点监测。包括研究机构、行业协会、学会、联合会等圈层组织。',
            ],
          },
          {
            title: '客户价值',
            context: [
              '构建云智能市场监测&洞察平台，帮助企业',
              '1、评估竞争对手，分析竞争环境，预测商业变化，学习成功经验，把握市场机会；',
              '2、获取竞对客户及客户动态，以便做出最适当的替换营销',
            ],
          },
        ],
      },
    ],
  },
  {
    target: '软件',
    targetId: 'software',
    articleList: [
      {
        title: '竞对客户挖掘，优先发现商机',
        secondTitle: '某全球企业应用软件头部厂商',
        desc: [
          {
            title: '案例背景',
            context: [
              '某企业应用软件厂商是全球企业应用软件市场的领头羊，产品包括ERP/HRM/CRM等，致力于帮助各行各业、各种规模的企业通过管理数字化实现卓越运营。该厂商借助机器学习、物联网和领先的分析技术，帮助客户转型为智慧企业。其端到端应用和服务套件可以帮助客户实现运营盈利，不断自我调整，并创造差异化优势。',
            ],
          },
          {
            title: '客户痛点',
            context: [
              '战略上从聚焦中大企业市场延伸到中小企业市场，即目标市场转型+扩张，如何快速找到中小市场中的高价值目标客户，并更早于与竞争对手发现商机是该厂商面临的重要挑战之一。',
            ],
          },
          {
            title: '解决方案',
            context: [
              '1、精准匹配目标客户：建立中小企业市场高价值客户挖掘与评估模型，并了解目标客户与哪些厂商有合作，合作的产品有哪些？',
              '2、优先发现商业机会：基于丰富的商情信息和数据挖掘能力，为其构建商业倾向模型，该模型通过考虑影响商业行为的变量，来评估目标客户购买/升级行为的可能性，帮助用户更早发现商机。',
            ],
          },
          {
            title: '客户价值',
            context: [
              '有效推动市场战略执行',
              '1、实现多维获客，帮助用户快速精准扩充中小企业市场客户储备量，并以客户评估分级结果进行有效资源分配；',
              '2、更早发现商机，通过对目标客户高时效性的动态监测，帮助市场和一线发现更多的商业机会。',
            ],
          },
        ],
      },
    ],
  },
  {
    target: 'ICT集成商',
    targetId: 'ict',
    articleList: [
      {
        title: '激活圈子活动，总部与区域无缝式作战赋能',
        secondTitle: '某ICT行业头部厂商',
        desc: [
          {
            title: '案例背景',
            context: [
              '该厂商为数字化解决方案领导者，拥有计算、存储、网络、5G、安全等全方位的数字化基础设施整体能力，提供云计算、大数据、人工智能、信息安全、智能联接、边缘计算等在内的一站式数字化解决方案和技术。该厂商始终以客户需求为导向，提供场景化解决方案，支持运营商、政府、金融、医疗、教育、交通、制造、电力、能源、互联网等行业数字化转型实践，产品和解决方案广泛应用于百余个国家和地区。',
            ],
          },
          {
            title: '客户痛点',
            context: [
              '1、客户储备量够吗？还有哪些客户值得去关注？',
              '2、联合哪些圈子做营销活动对影响客户更有效？',
              '3、联合哪些集成商伙伴更容易跟客户做成生意？',
            ],
          },
          {
            title: '解决方案',
            context: [
              '1、构建高价值客户挖掘与评估模型；',
              '2、构建高价值圈子识别模型；',
              '3、构建集成商伙伴挖掘与评估模型；',
              '4、梳理合作关系，客户与圈子关系，客户与集成商伙伴关系',
            ],
          },
          {
            title: '客户价值',
            context: [
              '1、挖掘到市场更多备选的有价值或者有机会的客户，有效扩充潜在高价值客户储备量；',
              '2、识别高价值圈子及其关系脉络，从而赋能圈子活动，有效触达客户；',
              '3、识别挖掘市场全量高价值集成商伙伴，大大提升客户生意转化的机会入口。',
            ],
          },
        ],
      },
    ],
  },
  {
    target: '无人零售',
    targetId: 'unmannedRetail',
    articleList: [
      {
        title: '精准匹配客户，精确触达客户',
        secondTitle: '某互联网无人零售头部运营商',
        desc: [
          {
            title: '案例背景',
            context: [
              '该企业是国内领先的无人零售运营商。企业通过自建物流仓配体系，围绕办公室群体提供多元场景、多元终端、多元商品的无人零售服务。并在2022年荣获新零售科技服务商创新排行榜TOP50。',
            ],
          },
          {
            title: '客户痛点',
            context: [
              '（1）Who?如何精准高效匹配目标商圈和潜在客户；',
              '（2）Where?如何提升客户地址准确性，提升拜访到达率；',
              '（3）How?如何提升网格化经营，提升拓客效率。',
            ],
          },
          {
            title: '解决方案',
            context: ['1、建立精准客户画像及商圈范围；', '2、深度挖掘客户真实的触达位置；', '3、依据位置进行网格化作战分配。'],
          },
          {
            title: '客户价值',
            context: [],
          },
        ],
      },
    ],
  },
  {
    target: '制造业',
    targetId: 'manufacturing',
    articleList: [
      {
        title: '海外商情站，全球商情洞察',
        secondTitle: '某头部家电制造行业',
        desc: [
          {
            title: '案例背景',
            context: [
              '该企业是一家覆盖智能家居、楼宇科技，工业技术、机器人与自动化和数字化创新业务为一体的全球化科技集团，每年为全球超过4亿用户，各领域的重要客户与战略合作伙伴提供满意的产品和服务。迄今，企业在全球拥有约200家子公司、30多个研发中心和30多个生产基地，业务覆盖200多个国家和地区。',
            ],
          },
          {
            title: '客户痛点',
            context: [
              '该企业2020年开始做海外业务转型，从海外OEM市场转向自有品牌市场，积极开发客户和渠道。随着战略推动，需要实时了解以下的海外市场信息：',
              '1、各地市场动态和政策信息；',
              '2、海外市场的活动、新闻及风险事件；',
              '3、各地行业协会、设计院等圈层信息；',
              '4、海外竞的综合动态和商业事件。',
            ],
          },
          {
            title: '解决方案',
            context: [
              '整合企业内外部资源，建设商业情报库服务平台。',
              '1、技术层面构建采集标注、多语翻译、识别抽取服务；',
              '2、数据层面构建国家/地区、行业、竞对等分级分类体系；',
              '3、应用层面构建情报检索、情报订阅、智能推送服务。',
            ],
          },
          {
            title: '客户价值',
            context: [
              '各区域业务人员统一使用“XX商情站”，实时了解所在地区市场关于行业、竞对、伙伴等最新的动态、新闻及事件，业务人员基于所获情报制定科学的行动策略，更快的获得商机，更大的抢占海外市场。',
            ],
          },
        ],
      },
    ],
  },
]

function handlerActiveKeyChange(val: Key) {
  const dom = document.getElementById(val as string)
  const scrollTop = dom?.offsetTop || 0

  $('html, body').animate({ scrollTop: scrollTop - 80 }, 300)
}

const { x, y } = useWindowScroll()
watch(y, (newVal) => {
  const cloudServicesDomTop = document.getElementById('cloudServices')?.getBoundingClientRect().top || 0
  const softwareDomTop = document.getElementById('software')?.getBoundingClientRect().top || 0
  const ictDomTop = document.getElementById('ict')?.getBoundingClientRect().top || 0
  const unmannedRetailDomTop = document.getElementById('unmannedRetail')?.getBoundingClientRect().top || 0
  const manufacturingDomTop = document.getElementById('manufacturing')?.getBoundingClientRect().top || 0

  const topList = [
    { id: 'cloudServices', value: Math.abs(cloudServicesDomTop) },
    { id: 'software', value: Math.abs(softwareDomTop) },
    { id: 'ict', value: Math.abs(ictDomTop) },
    { id: 'unmannedRetail', value: Math.abs(unmannedRetailDomTop) },
    { id: 'manufacturing', value: Math.abs(manufacturingDomTop) },
  ]

  const minItem = minBy(topList, (item: { value: number }) => item.value)
  if (!isEmpty(minItem)) {
    activeKey.value = minItem?.id
  }
})

onMounted(() => {
  // route.query
  const scrollTop = route.query.scrollTop as string
  if (scrollTop) {
    activeKey.value = scrollTop
    handlerActiveKeyChange(scrollTop)
  }
})

onUnmounted(() => {})
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';
@import '@/assets/less/main.less';

.case {
  .container {
    /* prettier-ignore */
    .media-query(xs,{padding: 32Px 32Px;});
    /* prettier-ignore */
    .media-query(sm,{padding: 32Px 32Px;});
    /* prettier-ignore */
    .media-query(md,{padding: 64Px 0;});
  }

  .banner {
    width: 100%;
    /* prettier-ignore */
    height: 262Px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: center;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }
    }

    // .container {
    //   z-index: 9;
    //   position: relative;
    //   // display: flex;
    //   // align-items: center;
    //   .bannerLeft {
    //     display: flex;
    //     flex-direction: column;
    //     justify-content: center;
    //     max-width: 550px;
    //     height: 100%;
    //   }

    //   .desc {
    //     max-width: 600px;
    //     font-weight: 400;
    //     font-size: 21px;
    //     color: #000000a8;
    //     margin: 29px 0 45px;

    //     .media-query(xs,{
    //         // width: 600px;
    //         font-size: 16px;
    //       });
    //     .media-query(sm,{
    //         max-width: 340px;
    //         margin: 12px 0;
    //         font-size: 18px;
    //       });
    //     .media-query(lg,{
    //         max-width: 600px;
    //         margin: 32px 0;
    //         font-size: 21px;
    //       });
    //   }

    //   .bannerImg {
    //     width: 100%;
    //     max-height: 392px;
    //     padding-left: 36px;
    //     margin: 0 0 0 auto;
    //     display: block;
    //     object-fit: contain;
    //   }
    // }
  }

  .ant-tabs {
    position: sticky;
    top: 0;
    margin-bottom: 16px;
    height: var(--nav-menu__height);
    z-index: 999;
    background-color: #fff;
    // line-height: var(--nav-menu__height);
  }
  :deep(.ant-tabs-tab) {
    width: 160px;
    justify-content: center;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    .media-query(xs,{ width: 110px;font-size: 14px; });
    .media-query(sm,{ width: 110px;font-size: 14px; });
    .media-query(md,{ width: 160px;font-size: 18px; });
    + .ant-tabs-tab {
      margin: 0;
    }
  }

  .articleItem {
    background: #fff;
    padding: 50px 124px;
    box-shadow: 0px 10px 30px 1px rgba(204, 204, 204, 0.4);
    border-radius: 0.8rem;
    // .itemBg {
    //   background-color: red;
    // }

    font-weight: 400;
    color: #555555;
    line-height: 2;

    .media-query(xs,{font-size: 14px;});
    .media-query(sm,{font-size: 14px;});
    .media-query(md,{font-size: 18px;});

    + .articleItem {
      margin-top: 64px;
    }

    .title {
      text-align: center;
      font-weight: 600;
      font-size: 36px;
      color: #333333;
      line-height: 2;

      .media-query(xs,{font-size: 22px;});
      .media-query(sm,{font-size: 22px;});
      .media-query(md,{font-size: 36px;});
    }
    .secondTitle {
      text-align: center;
      margin-bottom: 32px;
      font-weight: 450;
      font-size: 22px;

      .media-query(xs,{font-size: 18px;});
      .media-query(sm,{font-size: 18px;});
      .media-query(md,{font-size: 22px;});
    }
    .contextWarp {
      .warpItem {
        + .warpItem {
          /* prettier-ignore */
          margin-top: 24Px;
        }
        .warpTitle {
          font-weight: 450;
          color: @primary-color;
          display: flex;
          align-items: center;
          &::before {
            display: inline-block;
            /* prettier-ignore */
            margin-right: 8Px;
            border-radius: 2px;
            content: ' ';
            /* prettier-ignore */
            width: 4Px;
            /* prettier-ignore */
            height: 18Px;
            background-color: @primary-color;
          }
        }

        .warpImg {
          .media-query(xs,{width: 90%;});
          .media-query(sm,{width: 90%;});
          .media-query(md,{width: 70%;});
          margin: 0 auto;
          img {
            width: 100%;
            object-fit: contain;
          }
        }
      }
    }
  }
}
</style>
