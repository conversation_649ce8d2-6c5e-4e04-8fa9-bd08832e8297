<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2022-08-09 16:50:02
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-02 18:51:04
 * @FilePath: /busienginewebsite/pages/error/ExceptionPage.vue
 * @Description: 
-->
<template>
  <div class="exception">
    <a-row :gutter="[0, 32]" class="container">
      <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
        <div class="img">
          <img :src="typeItem.img" />
        </div>
      </a-col>
      <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
        <div class="content">
          <h1>{{ typeItem.title }}</h1>
          <div class="desc">{{ typeItem.desc }}</div>
          <div class="action">
            <a-button type="primary" @click="handleToHome">返回首页</a-button>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { useRouter } from 'vue-router'
import types from './type'

const props = defineProps({ type: { type: Number, default: 404 } })
const typeItem = computed(() => types[props.type])

const router = useRouter()
function handleToHome() {
  router.push({ path: '/' })
}
</script>

<style lang="less" scoped>
.exception {
  min-height: 500px;
  height: 80%;
  text-align: center;
  // display: flex;
  // align-items: center;
  // text-align: center;
  // justify-content: center;
  margin: 150px 0;

  .img {
    display: inline-block;
    padding-right: 52px;
    zoom: 1;
    height: 100%;
    img {
      height: 360px;
      max-width: 430px;
    }
  }
  .content {
    height: 100%;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    h1 {
      color: #434e59;
      font-size: 72px;
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 24px;
    }
    .desc {
      color: rgba(0, 0, 0, 0.45);
      font-size: 20px;
      line-height: 1.5;
      margin-bottom: 16px;
    }
  }
}

.mobile {
  .exception {
    margin-top: 30px;
    .img {
      padding-right: unset;

      img {
        height: 40%;
        max-width: 80%;
      }
    }
  }
}
</style>
