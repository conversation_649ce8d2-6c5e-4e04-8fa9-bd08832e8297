<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-08-18 18:03:18
 * @FilePath: /official-website/src/views/home/<USER>
 * @Description: 
-->
<template>
  <div class="home">
    <div class="banner">
      <img src="/images/common/bannerBg.png" srcset="/images/common/bannerBg.png 1x, /images/common/<EMAIL> 2x" class="bannerBg" />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }" class="bannerLeft">
          <h1 class="title">
            帮助企业获得
            <br />
            数据驱动的<strong>市场洞察力</strong>
          </h1>

          <div class="desc">基于海量开源数据，构建国内外市场洞察与智能营销解决方案，助力企业实现业务变革和商业增长</div>
          <div>
            <a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">申请体验</a-button>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <img class="bannerImg" src="/images/home/<USER>" srcset="/images/home/<USER>/images/home/<USER>" />
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container ourServices">
        <div class="header">
          <div class="headerTitle">我们服务</div>
          <!-- <div class="headerDesc">我们服务</div> -->
        </div>

        <a-tabs centered v-model:activeKey="activeKey" @change="handlerActiveKeyChange">
          <a-tab-pane v-for="(item, index) in ourServicesList" :key="index" :tab="item.title" forceRender>
            <a-row class="ourServicesItem">
              <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 8 }">
                <div class="textContainer">
                  <p class="itemTitle">{{ item.title }}</p>
                  <p class="itemSecondTitle">{{ item.secondTitle }}</p>
                  <div class="itemDesc">{{ item.desc }}</div>
                  <div>
                    <a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">申请体验</a-button>
                  </div>
                </div>
              </a-col>
              <a-col class="itemImg text-end" :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 16 }">
                <img
                  :src="`/images/home/<USER>"
                  :srcset="`
                      ${`/images/home/<USER>
                      ${`/images/home/<USER>
                    `"
                />
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <div class="container-bg2">
      <div class="container chooseUs">
        <div class="header">
          <div class="headerTitle">选择我们</div>
          <!-- <div class="headerDesc">选择我们</div> -->
        </div>

        <a-row :gutter="[16, 16]">
          <a-col v-for="(item, index) in chooseUsList" :key="index" :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 8 }">
            <div class="chooseUsItem">
              <div class="chooseUsItemBg">
                <div class="chooseUsItemTitle">
                  <img
                    :src="`/images/home/<USER>"
                    :srcset="`
                      ${`/images/home/<USER>
                      ${`/images/home/<USER>
                    `"
                  />
                  <p>{{ item.title }}</p>
                </div>
                <div class="chooseUsItemDesc">{{ item.desc }}</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
    <div class="container-bg1">
      <div class="container successCases">
        <div class="header">
          <div class="headerTitle">成功案例</div>
          <!-- <div class="headerDesc">成功案例</div> -->
        </div>

        <a-row :gutter="[16, 16]">
          <a-col :xs="{ span: 12 }" :sm="{ span: 12 }" :md="{ span: 6 }">
            <div class="caseItem cloudServices" @click="openDeatil('cloudServices')">
              <!-- <img src="http://iph.href.lu/305x553" /> -->
              <img src="/images/home/<USER>" srcset="/images/home/<USER>/images/home/<USER>" />
              <span class="caseText">云服务</span>
            </div>
          </a-col>
          <a-col :xs="{ span: 12 }" :sm="{ span: 12 }" :md="{ span: 6 }">
            <div class="caseItem software" @click="openDeatil('software')">
              <!-- <img src="http://iph.href.lu/305x553" /> -->
              <img src="/images/home/<USER>" srcset="/images/home/<USER>/images/home/<USER>" />
              <span class="caseText">软件</span>
            </div>
          </a-col>
          <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
            <a-row :gutter="[16, 16]">
              <a-col :span="24">
                <div class="caseItem" @click="openDeatil('ict')">
                  <!-- <img src="http://iph.href.lu/625x268" /> -->
                  <img src="/images/home/<USER>" srcset="/images/home/<USER>/images/home/<USER>" />
                  <span class="caseText">ICT集成商</span>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="caseItem" @click="openDeatil('unmannedRetail')">
                  <!-- <img src="http://iph.href.lu/305x268" /> -->
                  <img src="/images/home/<USER>" srcset="/images/home/<USER>/images/home/<USER>" />
                  <span class="caseText">无人零售</span>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="caseItem" @click="openDeatil('manufacturing')">
                  <!-- <img src="http://iph.href.lu/305x268" /> -->
                  <img
                    src="/images/home/<USER>"
                    srcset="/images/home/<USER>/images/home/<USER>"
                  />
                  <span class="caseText">制造业</span>
                </div>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'HomeIndex',
}
</script>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useEventBus } from '@vueuse/core'
const bus = useEventBus('showModal')

const ourServicesList = [
  {
    title: '行业洞察',
    secondTitle: 'Marketing Research',
    desc: '我们分析市场构成、细分领域以及主导企业，从全局视角掌握市场动态，通过系统化、科学化的方式帮助您深入了解市场和客户，为业务发展机会做出精准判断。',
    // imgUrl: ourServices1
  },
  {
    title: '竞争分析',
    secondTitle: 'Watch The Competition',
    desc: '我们帮助您识别潜在竞争对手，洞悉他们的策略行动，了解他们的客户和合作伙伴，让您在市场战争中以知己知彼，从容应对。',
    // imgUrl: ourServices2
  },
  {
    title: '客户定位',
    secondTitle: 'Find Customer',
    desc: '针对市场各异的客户群体我们绘制详细的客户画像构建客户价值评估系统，以识别更有价值的潜在客户优化您的客户群体。',
    // imgUrl: ourServices3
  },

  {
    title: '机会探索',
    secondTitle: 'Find Opportunities',
    desc: '我们针对B2B市场行为，建立了精密的统计模型和技术，通过考察影响商业行为的因素，精准评估商业机会的可能性，以便您能更快、更全面地掌握商业机会。',
    // imgUrl: ourServices4
  },
  {
    title: '海外情报',
    secondTitle: 'Overseas Intelligence',
    desc: '提供实时、全面的海外市场情报帮助出海企业洞察国际营商环境及时掌握海外风险事件，有效制定出海策略。',
    // imgUrl: ourServices5
  },
]
const chooseUsList = [
  {
    title: '坚实的数据基础',
    desc: '拥有亿级的企业数据和广泛的国内外媒体数据，通过多维度的价值标签帮助客户全面掌握行业和企业信息，科学的构建市场策略。',
    // imageUrl: chooseUs1
  },
  {
    title: '先进的算法能力',
    desc: '基于成熟的AI技术和丰富的数据支持，构建了适应B2B场景的30多种算法模型。支持满足客户多样化的业务需求。',
    // imageUrl: chooseUs2
  },
  {
    title: '深度的行业理解',
    desc: '将对B2B Marketing和行业的深刻理解，转化为具备行业洞察的算法模型及优秀产品，使产品应用更贴合业务场景。',
    // imageUrl: chooseUs3
  },
]

const router = useRouter()
function openDeatil(scrollTop: string) {
  router.push({
    path: '/case',
    query: { scrollTop },
  })
}

const activeKey = ref(0)
let stop: NodeJS.Timeout
// 设置循环切换tab
function setLoopTabs() {
  stop = setInterval(() => {
    if (activeKey.value < 4) {
      activeKey.value += 1
    } else {
      activeKey.value = 0
    }
  }, 5000)
}

function handlerActiveKeyChange() {
  clearInterval(stop)
  setLoopTabs()
}

onMounted(() => {
  setLoopTabs()
})

onUnmounted(() => {
  clearInterval(stop)
})
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';

.home {
  // font-size: 19.2px;
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));

    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 600Px;
    }

    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 653Px;
    });

    // .media-query(md,{
    //   /* prettier-ignore */
    //   min-height: 400Px;
    //   /* prettier-ignore */
    //   max-height: 653Px;
    // });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        height: 100%;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: cover;
      }
    }
  }

  .ourServices {
    .ourServicesItem {
      /* prettier-ignore */
      .media-query(xs,{ padding: 36Px 0; });
      /* prettier-ignore */
      .media-query(sm,{ padding: 36Px 0; });
      /* prettier-ignore */
      .media-query(md,{ padding: 74Px 0; });

      .textContainer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;

        .media-query(xs,{ text-align:center; });
        .media-query(sm,{ text-align:center; });
        /* prettier-ignore */
        .media-query(md,{ text-align:left; max-width: 387Px; });

        .itemTitle {
          font-weight: 600;
          font-size: 24px;
          color: #5851a2;
          /* prettier-ignore */
          margin-bottom: 6Px;
        }
        .itemSecondTitle {
          font-weight: 400;
          font-size: 18px;
          color: #7f7f7f;
          /* prettier-ignore */
          margin-bottom: 32Px;
        }
        .itemDesc {
          font-weight: 400;
          font-size: 18px;
          color: #666666;
          line-height: 2;
        }
        .ant-btn {
          /* prettier-ignore */
          margin-top: 36Px;
        }
      }
      .itemImg {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: right;
        /* prettier-ignore */
        .media-query(xs,{ margin-top:32Px });
        /* prettier-ignore */
        .media-query(sm,{ margin-top:32Px });
        /* prettier-ignore */
        .media-query(md,{ margin-top:0px; max-height: 430Px; });
        img {
          padding-left: 32px;
          object-fit: contain;
          .media-query(xs,{ width: 100%; });
          .media-query(sm,{ width: 100%; });
          .media-query(xl,{ width: auto; height: 100%; });
        }
      }
    }

    :deep(.ant-tabs-tab) {
      width: 160px;
      justify-content: center;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      .media-query(xs,{ width: 110px;font-size: 14px; });
      .media-query(sm,{ width: 110px;font-size: 14px; });
      .media-query(md,{ width: 160px;font-size: 18px; });
      + .ant-tabs-tab {
        margin: 0;
      }
    }
  }
  .chooseUs {
    padding-bottom: 70px;
    .chooseUsItem {
      /* prettier-ignore */
      max-width: 313Px;

      /* prettier-ignore */
      .media-query(md,{height: 410Px;});
      // background: #f7f8fb;
      border: 2px solid #ffffff;
      box-shadow: 0 13px 20px 0 #8b85a03b;
      /* prettier-ignore */
      border-radius: 8Px;
      padding: 10px;
      margin: 0 auto;

      .chooseUsItemBg {
        background-color: #fff;
        /* prettier-ignore */
        border-radius: 8Px;
        height: 100%;
        /* prettier-ignore */
        padding: 16Px;
      }
      .chooseUsItemTitle {
        font-weight: 600;
        font-size: 24px;
        padding-bottom: 12px;
        color: #502498;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #dddddd;

        img {
          /* prettier-ignore */
          width: 52Px;
          /* prettier-ignore */
          height: 58Px;
          margin-right: 9px;
        }
      }
      .chooseUsItemDesc {
        margin-top: 24px;
        // font-weight: 400;
        // font-size: 18px;
        color: #333333;
        line-height: 1.7;

        font-weight: 400;
        font-size: 18px;
        color: #333333;
        // line-height: 36px;
      }
    }
  }
  .successCases {
    padding-bottom: 96px;
    .caseItem {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      /* prettier-ignore */
      border-radius: 8Px;
      width: 100%;
      height: 100%;
      &:hover {
        img {
          transform: scale(1.2);
        }
      }
      // background: #f2f2f2;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.5s;
      }
      .caseText {
        position: absolute;
        left: 36px;
        bottom: 19px;
        font-weight: 600;
        font-size: 24px;
        color: #ffffff;
      }
    }

    .media-query(xs,{
      .cloudServices,
      .software {
        /* prettier-ignore */
        max-height: 268Px;
      }
    });
    .media-query(sm,{
      .cloudServices,
      .software {
        /* prettier-ignore */
        max-height: 268Px;
      }
    });
    .media-query(md,{
      .cloudServices,
      .software {
        max-height: 100%;
      }
    });
  }
}
</style>
