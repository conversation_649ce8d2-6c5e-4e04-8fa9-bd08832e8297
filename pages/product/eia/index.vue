<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-03-10 17:47:52
 * @FilePath: /busienginewebsite/pages/product/eia/index.vue
 * @Description: 
-->
<template>
  <div class="productEia">
    <div class="banner">
      <img src="/images/common/bannerBg.png" srcset="/images/common/bannerBg.png 1x, /images/common/<EMAIL> 2x" class="bannerBg" />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="title">B2B市场洞察平台</h1>

            <div class="desc">
              商瞳利用海量互联网数据和AI算法引擎，致力于为B2B企业打造一张覆盖行业、客户、圈层、伙伴的数字化市场全景图。通过市场洞察、客户挖掘、圈层识别、商机发现、竞对监测等功能，帮助企业制定最佳的营销策略。
            </div>

            <a-space>
              <a-button type="primary" size="large" class="fw-bold" href="//gaoguanshuo.com" target="_blank">
                前往个人版
              </a-button>
              <a-button size="large" class="fw-bold" @click="bus.emit('showModal')"> 体验企业版 </a-button>
            </a-space>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <img
            class="bannerImg"
            :src="`/images/product/eia/banner.png`"
            :srcset="`
              ${`/images/product/eia/banner.png`} 1x, 
              ${`/images/product/eia/<EMAIL>`} 2x
            `"
          />
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container marketingGrowth">
        <div class="header">
          <div class="headerTitle">助力营销增长</div>
        </div>

        <a-row :gutter="[16, 16]">
          <a-col v-for="(item, index) in marketingGrowthList" :key="index" :xs="{ span: 12 }" :sm="{ span: 12 }" :md="{ span: 6 }">
            <div class="marketingGrowthItem">
              <img
                class="marketingGrowthItemImg"
                :src="`/images/product/eia/marketingGrowth${index + 1}.png`"
                :srcset="`
                ${`/images/product/eia/marketingGrowth${index + 1}.png`} 1x, 
                ${`/images/product/eia/marketingGrowth${index + 1}@2x.png`} 2x
                `"
              />
              <p class="marketingGrowthItemTitle">{{ item.title }}</p>
              <div class="marketingGrowthItemDesc">{{ item.desc }}</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container methodology">
        <div class="header">
          <div class="headerTitle">行业先进方法论沉淀</div>
        </div>

        <div class="imgBox">
          <img
            :src="`//images/product/eia/methodology.png`"
            :srcset="`
            ${`/images/product/eia/methodology.png`} 1x, 
            ${`/images/product/eia/<EMAIL>`} 2x
            `"
          />
        </div>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container b2BMarket">
        <div class="header">
          <div class="headerTitle">B2B市场洞察与拓客平台</div>
        </div>

        <a-tabs centered v-model:activeKey="activeKey" @change="handlerActiveKeyChange">
          <a-tab-pane v-for="(item, index) in productInterfaceList" :key="index" :tab="item.title" forceRender>
            <a-row class="productInterfaceItem">
              <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 8 }">
                <div class="textContainer">
                  <p class="itemTitle">{{ item.title }}</p>
                  <div v-for="(descItem, index) in item.desc" :key="index" class="itemDesc">
                    {{ descItem }}
                  </div>
                  <div>
                    <a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">申请体验</a-button>
                  </div>
                </div>
              </a-col>
              <a-col class="itemImg text-end" :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 16 }">
                <img
                  :src="`/images/product/eia/productInterface${index + 1}.png`"
                  :srcset="`
                      ${`/images/product/eia/productInterface${index + 1}.png`} 1x, 
                      ${`/images/product/eia/productInterface${index + 1}@2x.png`} 2x
                    `"
                />
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container productAdvantages">
        <div class="header">
          <div class="headerTitle">产品优势</div>
        </div>

        <div class="productAdvantagesBox">
          <div class="productAdvantagesBoxBg">
            <a-row :gutter="32">
              <a-col v-for="(item, index) in productAdvantagesList" :key="index" :xs="{ span: 24 }" :sm="{ span: 12 }" :xl="{ span: 6 }">
                <div class="productAdvantagesItem">
                  <div class="productAdvantagesItemTitle">
                    <p>{{ item.title }}</p>
                  </div>
                  <div class="productAdvantagesItemDesc">{{ item.desc }}</div>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'EiaIndex',
}
</script>

<script setup lang="ts">
import { useEventBus } from '@vueuse/core'
import { onMounted, onUnmounted, ref } from 'vue'
const bus = useEventBus<string>('showModal')

const marketingGrowthList = [
  { title: '数据驱动', desc: '数据驱动的洞察力为商业决策提供有力支撑' },
  { title: '服务闭环', desc: '从策略到行动：市场洞察-商业脉络-目标清单-执行建议' },
  { title: '智能算法', desc: '丰富的B2B领域评估模型，高效识别与评判客户、圈层、伙伴' },
  { title: '易于拓展', desc: '无缝对接客户的CRM、Martech平台，助力企业数字化转型' },
]

const productInterfaceList = [
  {
    title: '行业洞察',
    desc: ['提供数据驱动的洞察力，包括市场规模、市场机会、竞对及友商情况、自身竞争力等方面的信息。'],
  },
  {
    title: '识别客户',
    desc: [
      '利用算法模型对海量企业进行扫描，为您推荐高潜力客户，同时对客户的匹配度和采购意向进行评估，以增加客户储备。',
      '-匹配度：评估范围包括工商类静态维度和增长、规模增长、招聘拓展等变化趋势。',
      '-意向度：评估企业需求程度与采购意向的水平，如新改项目、数字化诉求、年用云规模等',
    ],
  },
  {
    title: '圈层触达与转化',
    desc: [
      '-基于客户名单，扫描与识别市场上与客户关联的圈层组织，优选出高价值圈层，通过赞助或联合举办活动的方式批量触达与转化。',
      '-圈层组织包括协会、联盟、学会、研究机构、媒体等类型',
    ],
  },

  {
    title: '伙伴挖掘',
    desc: [
      '-汇聚行业内高质量系统集成商、代理商等伙伴资源，建立完整的企业伙伴库',
      '-识别伙伴与客户、伙伴与圈层的关系，实现三方资源的最优利用',
    ],
  },
  {
    title: '竞对监测',
    desc: ['监测现有和新的竞争对手，了解竞对的行为、活动、策略和新客户等信息，这将有利于制定更合理的竞争策略和竞争计划。'],
  },
]

const productAdvantagesList = [
  {
    title: '多元化的数据类型',
    desc: '聚集各种开放性数据类型，包括：工商、官网、招中标、招聘、新闻、社媒等，确保客户获得最全面的市场信息',
  },
  {
    title: '实时数据更新',
    desc: '日均收集1亿+条海量数据并储存，保证数据的高度时效性，帮助客户实时掌握市场动态',
  },
  {
    title: '60+行业/企业模型',
    desc: '依托行业领先企业的数字化服务经验，开发了60+行业/企业算法模型，帮助客户更深入、细致的了解市场',
  },
  {
    title: '先进的算法技术',
    desc: '平台内置了前沿的算法技术，能够对文本、图像、视频数据进行结构化处理，清洗降噪，归纳分析，还原市场真实面貌',
  },
]

const activeKey = ref(0)
let stop: NodeJS.Timeout
// 设置循环切换tab
function setLoopTabs() {
  stop = setInterval(() => {
    if (activeKey.value < 4) {
      activeKey.value += 1
    } else {
      activeKey.value = 0
    }
  }, 5000)
}

function handlerActiveKeyChange() {
  clearInterval(stop)
  setLoopTabs()
}

onMounted(() => {
  setLoopTabs()
})

onUnmounted(() => {
  clearInterval(stop)
})
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';

.productEia {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 600Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 653Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        height: 100%;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: cover;
      }
    }
  }

  .marketingGrowth {
    padding-bottom: 90px;
    .marketingGrowthItem {
      /* prettier-ignore */
      max-width: 260Px;
      margin: 0 auto;
      .marketingGrowthItemImg {
        display: block;
        /* prettier-ignore */
        width: 54Px;
        /* prettier-ignore */
        height: 62Px;
        margin: 0 auto 22px;
      }
      .marketingGrowthItemTitle {
        font-weight: 500;
        font-size: 24px;
        color: #5851a2;
        text-align: center;
        margin-bottom: 26px;
      }
      .marketingGrowthItemDesc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        text-align: center;
      }
    }
  }

  .methodology {
    /* prettier-ignore */
    padding-bottom: 68Px;
    .imgBox {
      // width: 1236px;
      text-align: center;
      img {
        object-fit: cover;
        width: 100%;
        /* prettier-ignore */
        max-height: 452Px;
      }
    }
  }

  .b2BMarket {
    .productInterfaceItem {
      /* prettier-ignore */
      .media-query(xs,{ padding: 36Px 0; });
      /* prettier-ignore */
      .media-query(sm,{ padding: 36Px 0; });
      /* prettier-ignore */
      .media-query(md,{ padding: 74Px 0; });

      .textContainer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;

        .media-query(xs,{ text-align:center; });
        .media-query(sm,{ text-align:center; });
        /* prettier-ignore */
        .media-query(md,{ text-align:left; max-width: 387Px; });

        .itemTitle {
          font-weight: 600;
          font-size: 24px;
          color: #5851a2;
          /* prettier-ignore */
          margin-bottom: 6Px;
        }
        .itemSecondTitle {
          font-weight: 400;
          font-size: 18px;
          color: #7f7f7f;
          /* prettier-ignore */
          margin-bottom: 32Px;
        }
        .itemDesc {
          font-weight: 400;
          font-size: 18px;
          color: #666666;
          line-height: 2;
        }

        .ant-btn {
          /* prettier-ignore */
          margin-top: 36Px;
        }
      }
      .itemImg {
        // height: 100%;
        display: flex;
        align-items: center;
        justify-content: right;
        /* prettier-ignore */
        .media-query(xs,{ margin-top:32Px });
        /* prettier-ignore */
        .media-query(sm,{ margin-top:32Px });
        /* prettier-ignore */
        .media-query(md,{ margin-top:0px; max-height: 430Px; });
        img {
          padding-left: 32px;
          object-fit: contain;
          .media-query(xs,{ width: 100%; });
          .media-query(sm,{ width: 100%; });
          .media-query(xl,{ width: auto; height: 100%; });
        }
      }
    }

    :deep(.ant-tabs-tab) {
      width: 160px;
      justify-content: center;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      .media-query(xs,{ width: 110px;font-size: 14px; });
      .media-query(sm,{ width: 110px;font-size: 14px; });
      .media-query(md,{ width: 160px;font-size: 18px; });
      + .ant-tabs-tab {
        margin: 0;
      }
    }
  }
  .productAdvantages {
    padding-bottom: 70px;
    .productAdvantagesBox {
      /* prettier-ignore */
      // max-width: 313Px;

      // background: #f7f8fb;
      border: 2px solid #ffffff;
      box-shadow: 0 13px 20px 0 #8b85a03b;
      /* prettier-ignore */
      border-radius: 8Px;
      padding: 10px;
      margin: 0 auto;

      .productAdvantagesBoxBg {
        background-color: #fff;
        /* prettier-ignore */
        border-radius: 8Px;
        height: 100%;
        /* prettier-ignore */
        padding: 0 42Px;
      }

      .productAdvantagesItem {
        /* prettier-ignore */
        .media-query(xs,{min-height: 280Px;});
        /* prettier-ignore */
        .media-query(sm,{min-height: 280Px;});
        /* prettier-ignore */
        .media-query(md,{min-height: 310Px;});
        /* prettier-ignore */
        .media-query(xl,{min-height: 410Px;});
        .productAdvantagesItemTitle {
          font-weight: 600;
          font-size: 24px;
          padding: 24px 0;
          color: #502498;
          align-items: left;
          border-bottom: 1px solid #dddddd;

          img {
            /* prettier-ignore */
            width: 52Px;
            /* prettier-ignore */
            height: 58Px;
            margin-right: 9px;
          }
        }
        .productAdvantagesItemDesc {
          margin-top: 24px;
          // font-weight: 400;
          // font-size: 18px;
          color: #333333;
          line-height: 1.7;

          font-weight: 400;
          font-size: 18px;
          color: #333333;
          // line-height: 36px;
        }
      }
    }
  }
}
</style>
