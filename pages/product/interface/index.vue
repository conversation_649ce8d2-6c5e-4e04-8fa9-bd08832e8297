<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-09-11 18:23:21
 * @FilePath: /busienginewebsite/src/views/product/interface/index.vue
 * @Description: 
-->
<template>
  <div class="productInterface">
    <div class="banner">
      <img
        :src="`${`/images/common/bannerBg.png`}`"
        :srcset="`
          ${`/images/common/bannerBg.png`} 1x, 
          ${`/images/common/<EMAIL>`} 2x
        `"
        class="bannerBg"
      />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="title">数据API</h1>

            <div class="desc">
              指数动力为企业提供高时效、高性能的 B2B
              数据API，包含行业、企业、圈层、友商等多种类型的数据，无缝连接企业内部系统进行数据补全，助力企业数字化转型。
            </div>
            <div><a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">申请体验</a-button></div>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <img
            class="bannerImg"
            :src="`/images/product/interface/banner.png`"
            :srcset="`
              ${`/images/product/interface/banner.png`} 1x, 
              ${`/images/product/interface/<EMAIL>`} 2x
            `"
          />
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container">
        <a-row class="generalSituation" :gutter="[32, 32]">
          <a-col class="generalSituationItem">
            <div>
              <p>1.6亿+</p>
              <span>市场主体数据</span>
            </div>
          </a-col>
          <a-col class="generalSituationItem">
            <div>
              <p>2000+</p>
              <span>行业圈层</span>
            </div>
          </a-col>
          <a-col class="generalSituationItem">
            <div>
              <p>40万+</p>
              <span>行业分类</span>
            </div>
          </a-col>
          <a-col class="generalSituationItem">
            <div>
              <p>7500+</p>
              <span>新闻数据源</span>
            </div>
          </a-col>
          <a-col class="generalSituationItem">
            <div>
              <p>60+</p>
              <span>算法模型</span>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <div class="container-bg2">
      <div class="dataValue">
        <div class="container">
          <rowWrap class="interfaceWarp">
            <template #leftBox>
              <div class="leftBox">
                <div class="title">深度挖掘数据价值<br />提升场景应用效果</div>
                <div class="desc">以标签形式展示算法模型的分析结果，帮助用户更高效的理解和使用这些信息，深度发掘数据价值。</div>
              </div>
            </template>
            <template #rightBox>
              <div class="rightBox justify-content-end">
                <div>
                  <img
                    :src="`/images/product/interface/dataValue.png`"
                    :srcset="`
                    ${`/images/product/interface/dataValue.png`} 1x, 
                    ${`/images/product/interface/<EMAIL>`} 2x
                  `"
                  />
                </div>
              </div>
            </template>
          </rowWrap>
        </div>
      </div>
    </div>
    <div class="container-bg1">
      <div class="container algorithmsModels">
        <rowWrap class="interfaceWarp" isReverse>
          <template #leftBox>
            <div class="leftBox">
              <div class="title">
                <a-row>
                  <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 0 }" :lg="{ span: 24 }">
                    基于B2B场景数据的 <br />
                    算法与模型能力
                  </a-col>
                  <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 24 }" :lg="{ span: 0 }">
                    基于B2B场景数据的算法与模型能力
                  </a-col>
                </a-row>
              </div>
              <div class="desc">
                <p>通过考虑影响商业行为的各类因素来评估商机的可能性，生成一系列意向类标签和指标。</p>
                <ul>
                  <li>新获得资金：融资事件、上市</li>
                  <li>数字化：新改扩建项目、IT岗位扩张、媒体数字化声量飙升、获取新资质</li>
                  <li>招中标：新招标、新中标</li>
                  <li>招聘岗位：招聘扩张、新城市招聘、海外岗位招聘</li>
                  <li>其它：开设新机构、高管变动</li>
                </ul>
              </div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-start">
              <div>
                <img
                  :src="`/images/product/interface/model.png`"
                  :srcset="`
                    ${`/images/product/interface/model.png`} 1x, 
                    ${`/images/product/interface/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
    <div class="container-bg2">
      <div class="container digitalSystem">
        <rowWrap class="interfaceWarp">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">数字化系统接入</div>
              <div class="desc">数据API 能够无缝对接 CRM、Martech 系统，完成主动推送和实时更新，实现数据流转自动化，让数据赋能业务。</div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/product/interface/digitization.png`"
                  :srcset="`
                    ${`/images/product/interface/digitization.png`} 1x, 
                    ${`/images/product/interface/<EMAIL>`} 2x
                    `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
    <div class="container-bg1">
      <div class="container portraitCompletion">
        <rowWrap class="interfaceWarp" isReverse>
          <template #leftBox>
            <div class="leftBox">
              <div class="title">数据查询、数据分析、画像补全</div>
              <div class="desc">
                基于 API 查询、接入、补全客户基础信息和意向情报，清晰掌握客户的业务与技术背景，客观的评估线索质量，从而提高销售效率。
              </div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-start">
              <div>
                <img
                  :src="`/images/product/interface/query.png`"
                  :srcset="`
                    ${`/images/product/interface/query.png`} 1x, 
                    ${`/images/product/interface/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'InterfaceIndex',
}
</script>

<script setup lang="ts">
import rowWrap from '@comp/rowWrap/index.vue'
import { useEventBus } from '@vueuse/core'
const bus = useEventBus('showModal')
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';
.productInterface {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 600Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 653Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        height: 100%;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: cover;
      }
    }
  }

  .generalSituation {
    /* prettier-ignore */
    padding: 72Px 0;
    justify-content: center;
    .generalSituationItem {
      text-align: center;

      .media-query(xs,{width:50%;});
      .media-query(sm,{width:50%;});
      .media-query(md,{width:33%;});
      .media-query(lg,{width:20%;});
      /* prettier-ignore */
      @media (max-width: 425Px) {
        width:100%;
      }
      p {
        font-weight: 600;
        font-size: 48px;
        color: #5851a2;
      }
      span {
        /* prettier-ignore */
        margin-top: 12Px;
        font-weight: 400;
        font-size: 24px;
        color: #555555;
      }
    }
  }

  .interfaceWarp {
    /* prettier-ignore */
    padding: 64Px 0;
    .leftBox {
      height: 100%;
      max-width: 480px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .media-query(xs,{ max-width: 100%; text-align: center; });
      .media-query(sm,{ max-width: 100%; text-align: center; });
      .media-query(md,{ text-align: left; });

      .title {
        font-weight: 600;
        font-size: 36px;
        color: #333333;
      }
      .desc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        line-height: 2;
        /* prettier-ignore */
        margin-top: 30Px;
      }
      ul {
        list-style: circle;
      }
    }
    .rightBox {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      @media (max-width: 768px) {
        justify-content: center !important;
      }
      img {
        width: 100%;
        /* prettier-ignore */
        max-height: 520Px;
        object-fit: contain;
      }
    }
  }

  .dataValue {
    .rightBox {
      img {
        /* prettier-ignore */
        max-height: 519Px;
      }
    }
  }
  .algorithmsModels {
    .rightBox {
      img {
        /* prettier-ignore */
        max-height: 504Px;
      }
    }
  }
  .digitalSystem {
    .rightBox {
      img {
        /* prettier-ignore */
        max-height: 532Px;
      }
    }
  }
  .portraitCompletion {
    .rightBox {
      img {
        /* prettier-ignore */
        max-height: 441Px;
      }
    }
  }
}
</style>
