<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-12 16:40:16
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-11-11 16:47:12
 * @FilePath: /corp-elf-web-consumer/src/views/overview/components/circle.vue
 * @Description: 
-->
<template>
  <a-card title="洞察与客户互动的高影响力圈子" :bordered="false" :bodyStyle="{ minHeight: '285px' }">
    <a-config-provider :theme="{ components: { Table: { fontSize: '16px' } } }">
      <a-table size="small" :dataSource="dataList" :columns="columns" :loading="loading" :pagination="false">
        <template #bodyCell="{ text, column }">
          <template v-if="column.dataIndex === 'clientCnt'">
            <span :style="{ color: theme.getColorPrimary }" class="pr16px">
              {{ text ? text : '-' }}
            </span>
          </template>
        </template>
      </a-table>
    </a-config-provider>
  </a-card>
</template>

<script setup lang="ts">
import { layerShowLayer } from '@/api/api'
import useListLoading from '@/hooks/useListLoading'
import { useThemeStore } from '@/store'
import { UAParser } from 'ua-parser-js'
import { computed } from 'vue'
import { ShowLayerRequest } from '~/types/api/layer/showLayer'

const theme = useThemeStore()

const parser = new UAParser()
const { browser, device } = parser.getResult()
const isWechat = computed(() => browser.name === 'WeChat' && device.type === 'mobile')

const columns = isWechat.value
  ? [
      { title: '圈层', dataIndex: 'layerName', ellipsis: true },
      { title: '所在地', dataIndex: 'province', width: 90, align: 'center' }
    ]
  : [
      { title: '圈层', dataIndex: 'layerName', ellipsis: true },
      { title: '所在地', dataIndex: 'province', width: 140, align: 'center' },
      { title: '影响客户数量', dataIndex: 'clientCnt', align: 'center', width: 140 }
    ]

const params = computed<ShowLayerRequest>(() => ({
  pageNo: 1,
  pageSize: 10,
  filter: {
    // industryId: props.industryId!,
    isOnlyOwner: true,
    isHidCustomer: false,
    orderColumns: [
      {
        columnName: 'client_cnt2',
        asc: false
      }
    ]
  }
}))
const { dataList, loading } = useListLoading(layerShowLayer, params)
</script>

<style scoped></style>
