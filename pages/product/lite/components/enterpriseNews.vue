<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-12 16:40:16
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-04 10:58:00
 * @FilePath: /corp-elf-web-consumer/src/views/overview/components/enterpriseNews.vue
 * @Description: 
-->
<template>
  <a-card title="监控客户活动，捕获潜在线索" :bordered="false" :bodyStyle="{ padding: 0, minHeight: '285px' }">
    <a-list class="list" item-layout="horizontal" :data-source="dataList" :loading="{ spinning: infiniteLoading, tip: '加载中...' }">
      <!-- <template #loadMore>
        <div v-if="dataList.length > 0" class="loadMore py-8px">
          <div
            v-if="isEmpty(userStore.getToken)"
            class="inline-flex items-center justify-center hoverPrimaryColor endText"
            @click="showLoginModal"
          >
            <iconfontIcon icon="icon-lock-on" />
            <p class="mx4px">登录即可获取更多数据</p>
            <iconfontIcon icon="icon-chevron-down" />
          </div>
          <div v-else v-intersection-observer="handlerIntersectionObserver" class="endText">
            <p v-if="!noMore"><a-spin tip="加载中..."></a-spin></p>
            <p v-else>没有更多了</p>
          </div>
        </div>
      </template> -->
      <template #renderItem="{ item }">
        <newsCard :newsData="item" @click="newsCardClick(item)" :mainContentMaxLines="3" />
      </template>
    </a-list>
  </a-card>
</template>

<script setup lang="ts">
import { industryHotNews } from '@/api/api'
import newsCard from '@/components/newsCard/index.vue'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { HotNewsRequest, HotNewsResponse } from '~/types/api/industry/hotNews'

// 获取关注企业动态的参数
const params: HotNewsRequest = {
  collectId: '-1',
  timeRange: 'ALL',
  eventTypes: undefined
}
const {
  dataList,
  loading: infiniteLoading,
} = useInfiniteLoading(industryHotNews, params, { pageParams: { pageSize: 7 } })

function newsCardClick(item: HotNewsResponse) {
  window.open(item.oriUrl, '_blank')
}
</script>

<style scoped></style>
