<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-12 16:40:16
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-13 17:45:35
 * @FilePath: /corp-elf-web-consumer/src/views/overview/components/executiveSaid.vue
 * @Description: 
-->
<template>
  <a-card title="通过决策人的言论，了解其背景、偏好和决策风格" :bordered="false" :bodyStyle="{ padding: 0, minHeight: '285px' }">
    <a-list :loading="infiniteLoading" item-layout="horizontal" :data-source="dataList">
      <template #renderItem="{ item }: { item: executiveSaidListResType }">
        <executiveSaidCard :item="item" :mainContentMaxLines="3" class="background-#ffffff00!" dateFormat="年月日" />
      </template>
    </a-list>
  </a-card>
</template>

<script setup lang="ts">
import { executiveSaidList } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import { executiveSaidListReqType, executiveSaidListResType } from '~/types/api/executiveSaid/executiveSaidList'
import executiveSaidCard from '@/components/executiveSaidCard/index.vue'

type executiveParamsType = Pick<executiveSaidListReqType, 'showPostType' | 'followCompany'>

const executiveParams: executiveParamsType = { showPostType: 1, followCompany: true }

const { dataList, loading: infiniteLoading } = useInfiniteLoading(executiveSaidList, executiveParams, { pageParams: { pageSize: 3 } })
</script>

<style scoped></style>
