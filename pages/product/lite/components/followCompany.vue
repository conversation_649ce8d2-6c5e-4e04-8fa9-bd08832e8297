<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2024-09-12 16:40:03
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 14:39:32
 * @FilePath: /busienginewebsite/pages/product/lite/components/followCompany.vue
 * @Description: 
-->
<template>
  <a-card :bordered="false" title="发现潜在客户">
    <a-config-provider :theme="{ components: { Table: { fontSize: '16px', margin: '16px 0 0' } } }">
      <!-- <a-table :columns="columns" :data-source="dataList" :loading="loading" size="small" :pagination="false">
        <template #bodyCell="{ text, record, column }">
          <template v-if="column.dataIndex === 'entName'">
            <div class="flex">
              <a-tag>{{ record.collectName }}</a-tag>
              <p
                class="flex-1 ellipsis hoverPrimaryColor"
                @click="openCompanyInfo({ companyId: record.companyId, companyName: record.entName })"
              >
                {{ text }}
              </p>
            </div>
          </template>
          <template v-if="column.dataIndex === 'address'">
            {{ transformLocation(record) }}
          </template>
        </template>
      </a-table> -->
    </a-config-provider>
    {{ data }}
  </a-card>
</template>

<script setup lang="ts">
import { transformLocation } from '@/utils/util'
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const columns = [
  { title: '企业名称', dataIndex: 'entName', ellipsis: true },
  // { title: '近期动态', dataIndex: 'events', width: '120px', ellipsis: true }
  { title: '地区', dataIndex: 'address', slotName: 'address', ellipsis: true, align: 'center' },
]
const params = computed(() => ({
  collectId: '-1',
  companyName: undefined,
  isFirst: false,
  customerType: 'PERSONAL',
  isAsc: false,
  orderColumns: [
    {
      columnName: 'powerful_rank_score',
      asc: false,
    },
  ],
}))

const { data, pending, error } = await useAsyncData('customerList', () =>
  $fetch('/corp_elf/context/corpElf', {
    method: 'POST',
    query: {
      key: new Date().getTime(),
    },
    body: {
      method: 'POST',
      mtop: '/customer/list',
      param: null,
      serviceId: 'corp_elf',
      body: params.value,
    },
    headers: {
      'Content-Type': 'application/json',
    },
  })
)

// const { data, error, execute } = await useFetch('/corp_elf/context/corpElf', {
//   method: 'POST',
//   body: {
//     method: 'POST',
//     mtop: '/customer/list',
//     param: null,
//     serviceId: 'corp_elf',
//     body: params.value,
//   },
//   headers: {
//     'Content-Type': 'application/json',
//   },
//   // immediate: false,
// })

onMounted(() => {
  console.log('data: ', data.value)
  // execute()
  console.log(12122)
})
// const { dataList, loading } = useListLoading(async () => {

// }, params)
</script>

<style scoped></style>
