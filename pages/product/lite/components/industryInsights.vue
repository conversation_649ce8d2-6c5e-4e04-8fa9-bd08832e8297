<template>
  <a-card title="跟踪行业标杆客户活动" :bordered="false" :bodyStyle="{ padding: 0, minHeight: '285px' }">
    <a-list :loading="infiniteLoading" item-layout="horizontal" :data-source="dataList">
      <template #renderItem="{ item }: { item: industryCompanyViewpointResponseType }">
        <a-list-item>
          <div class="caseItem">
            <div class="titleContent flex items-center">
              <iconfontIcon icon="icon-faxian" :extraCommonProps="{ class: ['!fs-30px mr-8px'] }" />
              <div class="flex-1">
                <a-typography-title :level="5" class="companyName hoverPrimaryColor inline-block" @click="openCompanyInfo(item)">
                  {{ item.companyAbbr || '' }}
                </a-typography-title>
              </div>
            </div>

            <div v-for="(listItem, index) in item.list" :key="index">
              <div class="mainContent">
                <div class="time">
                  <a-typography-text type="secondary">{{ getPublishDate(listItem.publishDate) }}</a-typography-text>
                </div>
                <div class="content color-#7F7F7F fs-16px ellipsis-8 text-justify">
                  {{ listItem.viewpointContent }}
                </div>
              </div>

              <div class="secondaryContent pl2em color-#00000073 text-right">
                <text-clamp
                  :max-lines="1"
                  location="middle"
                  :text="`—— 《${listItem.title.replace(/\.[^\.]+$/g, '')}》 ${listItem.author}`"
                  :class="listItem.informationType === 'NEWS' && !isEmpty(listItem.sourceUrl) ? 'hoverPrimaryColor' : ''"
                  @click="openSourceUrl(listItem)"
                >
                </text-clamp>
              </div>
            </div>
          </div>
        </a-list-item>
      </template>
    </a-list>
  </a-card>
</template>

<script setup lang="ts">
import { industryInformationListIndustryCompanyViewpoint } from '@/api/api'
import useInfiniteLoading from '@/hooks/useInfiniteLoading'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'
import {
  companyCaseViewpointType,
  industryCompanyViewpointRequestType,
  industryCompanyViewpointResponseType
} from '~/types/api/industry/Information/industryCompanyViewpoint'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const params: industryCompanyViewpointRequestType = {
  industryId: '3394', // props.industryId!, // 行业ID
  viewpointType: 'CASE', // 观点类型
  keyword: undefined, // 关键字
  startTime: undefined, // 开始时间
  endTime: undefined // 结束时间
}
const { dataList, loading: infiniteLoading } = useInfiniteLoading(industryInformationListIndustryCompanyViewpoint, params, {
  pageParams: { pageSize: 3 }
})

// 解析日期
const getPublishDate = (date: number | string) => {
  return dayjs(date).format('YYYY-MM-DD')
  const diffDay = dayjs(date).set('h', 0).set('m', 0).set('s', 0)
  const nowDay = dayjs().set('h', 0).set('m', 0).set('s', 0)
  const diffRes = nowDay.diff(diffDay, 'd')
  if (diffRes < 1) {
    return '今日'
  } else if (diffRes === 1) {
    return '昨日'
  } else {
    return dayjs(date).format('M月D日')
  }
}

// 打开企业
const router = useRouter()
function openCompanyInfo(caseItem: industryCompanyViewpointResponseType) {
  console.log('caseItem: ', caseItem)
  if (caseItem.companyUniId) {
    router.push({
      path: '/companyInfo/index',
      name: 'companyInfo-index',
      query: {
        companyId: caseItem.companyUniId,
        companyName: caseItem.companyName
      }
    })
  } else {
    message.warning('该组织详情正在准备中')
  }
}

function openSourceUrl(item: companyCaseViewpointType) {
  if (item.informationType === 'NEWS' && !isEmpty(item.sourceUrl)) {
    window.open(item.sourceUrl, '_blank')
  }
}
</script>

<style scoped>
.caseItem {
  &:hover {
    .loadMore {
      visibility: initial;
    }
  }

  .titleContent {
    margin-bottom: 4px;
    .companyName {
      margin-bottom: 0px;
    }
  }

  .mainContent {
    .time {
      margin-bottom: 4px;
    }
    .content {
      margin-bottom: 4px;
      white-space: pre-wrap;
    }
  }

  .secondaryContent {
  }

  .loadMore {
    visibility: hidden;
    height: 20px;
  }
}
</style>
