<template>
  <a-card title="了解行业智库观点" :bodyStyle="{ minHeight: '285px' }" :bordered="false">
    <a-row :gutter="[16, 16]">
      <a-col :span="12" v-for="(viewpointVal, viewpointKey) in knowledgeViewpointData" :key="`${props.industryId}-${viewpointKey}`">
        <div class="viewpointItem">
          <div class="viewpointTitle flex items-center mb8px">
            <iconfontIcon icon="icon-faxian" :extraCommonProps="{ class: ['!fs-30px mr-8px'] }" />
            <a-typography-title :level="5" class="flex-1 !mb0">{{ viewpointKey }}</a-typography-title>
          </div>
          <ul class="viewpointContent overflow-hidden">
            <template v-if="!isEmpty(viewpointVal)">
              <li v-for="(item, index) in viewpointVal" :key="index" class="fs-16px flex items-start">
                <div class="block w30px text-center mr-6px point">
                  <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !fs-12px'] }" />
                </div>

                <!-- <p class="flex-1 ellipsis-5 text-justify">{{ item.viewpointContent }}</p> -->
                <!-- <text-clamp :max-lines="5" :text="item.viewpointContent" class="flex-1 ellipsis-5"> </text-clamp> -->
                <p class="flex-1 ellipsis-5">{{ item.viewpointContent }}</p>
              </li>
            </template>
            <template v-else>
              <empty />
            </template>
          </ul>
          <!-- 更多观点 -->
          <!-- <div v-if="!isEmpty(viewpointVal)">
              <OverviewDetailModal
                :title="viewpointKey"
                :industryId="props.industryId"
                :viewpointType="find(props.knowledgeTypeList, { viewpointTypeName: viewpointKey })"
                :viewpointList="viewpointVal"
              />
            </div> -->
        </div>
      </a-col>
    </a-row>
  </a-card>
</template>

<script setup lang="ts">
import { listIndustrySummaryViewpoint } from '@/api/api'
import iconfontIcon from '@/components/tools/iconfontIcon'
import { find, isEmpty } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { ListIndustrySummaryViewpointResponse } from '~/types/api/industry/Information/listIndustrySummaryViewpoint'

const props = withDefaults(defineProps<{ industryId?: string | undefined }>(), {
  // knowledgeTypeList: ViewpointTypeList[] | undefined
  industryId: '3394'
})

const loading = ref(false)

const knowledgeViewpointData = ref<Record<string, ListIndustrySummaryViewpointResponse[]>>({
  现状: [],
  前景: [],
  环境: [],
  挑战: []
  // 机遇: []
})
/**
 * @description: 获取洞见观点
 * @return {*}
 */
async function getKnowledgeViewpoint() {
  try {
    loading.value = true
    const { result } = await listIndustrySummaryViewpoint({
      industryId: props.industryId!
      // viewpointType: 'CHALLENGE'
    })
    let viewPointList: ListIndustrySummaryViewpointResponse[] = []
    result.forEach(item => {
      const tempList: ListIndustrySummaryViewpointResponse[] = item.viewpointContent.split('\n').map(textItem => ({
        industryId: item.industryId,
        viewpointContent: textItem,
        viewpointTypeName: item.viewpointTypeName
      }))
      viewPointList = viewPointList.concat(tempList)
    })
    // knowledgeViewpointData.value = groupBy(viewPointList, 'viewpointTypeName')
    const tempData: Record<string, ListIndustrySummaryViewpointResponse[]> = {
      现状: [],
      前景: [],
      环境: [],
      挑战: [],
      机遇: []
    }
    viewPointList.forEach(item => tempData[item.viewpointTypeName].push(item))
    // knowledgeViewpointData.value = tempData
    console.log('tempData: ', tempData)
    knowledgeViewpointData.value = {
      // 现状: tempData['现状'],
      // 前景: tempData['前景']
      现状: tempData['现状'],
      前景: tempData['前景'],
      环境: tempData['环境'],
      挑战: tempData['挑战']
    }
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

onMounted(() => {
  getKnowledgeViewpoint()
})
</script>

<style scoped lang="less">
.viewpointItem {
  .viewpointTitle {
  }
  .viewpointContent {
    height: 294px;
    li {
      // margin-bottom: 8px;
    }
    p {
      line-height: 2;
    }
    // @media screen and (min-height: 768px) {
    //   height: 294px;
    // }
    // @media screen and (min-height: 1200px) {
    //   height: 422px;
    // }
  }
}
</style>
