<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 14:17:35
 * @FilePath: /busienginewebsite/pages/product/lite/index.vue
 * @Description: 
-->
<template>
  <div class="productLite bg-#fafafe">
    <div class="banner">
      <img src="/images/common/bannerBg.png" srcset="/images/common/bannerBg.png 1x, /images/common/<EMAIL> 2x" class="bannerBg" />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="color-#000 fw-600 font-size-42px">帮助ToB人提升客户洞察力</h1>
            <ul :class="['font-size-22px color-#000000a8 mt32px', 'mb48px']">
              <li class="flex items-start mb8px">
                <div class="block w30px text-center mr-6px point">
                  <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !font-size-12px'] }" />
                </div>
                挖掘潜在客户
              </li>
              <li class="flex items-start mb8px">
                <div class="block w30px text-center mr-6px point">
                  <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !font-size-12px'] }" />
                </div>
                跟踪客户活动，捕获潜在线索
              </li>
              <li class="flex items-start mb8px">
                <div class="block w30px text-center mr-6px point">
                  <iconfontIcon icon="icon-point" :extraCommonProps="{ class: [' !font-size-12px'] }" />
                </div>
                了解决策人的背景、偏好和决策风格
              </li>
            </ul>
            <a-space :size="8" direction="vertical">
              <a-button type="primary" size="large"><router-link to="/home"> 立即登录 </router-link></a-button>
              <span class="font-size-20px fw-500 color-#000000a8">注册赠送 7 天会员</span>
            </a-space>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <img
            class="bannerImg"
            src="/images/product/eia/banner.png"
            srcset="/images/product/eia/banner.png 1x, /images/product/eia/<EMAIL> 2x"
          />
        </a-col>
      </a-row>
    </div>

    <!-- :class="['m-x-auto mt64px mb16px', !isWechat ? 'max-w1260px' : 'px32px']" -->

    <!-- <template v-if="isWechat">
          <a-space direction="vertical" :size="16" class="w100%">
            <FollowCompany />
            <ExecutiveSaid />
            <EnterpriseNews />
            <Circle />
            <IndustryInsights />
            <Viewpoint />
          </a-space>
        </template> -->

    <!-- <template v-else> -->
    <div class="container mx-auto mt-64px">
      <a-row :gutter="16">
        <a-col :span="24">
          <h2 class="font-size-28px fw-600 mb16px">商瞳的服务</h2>
        </a-col>
        <a-col :span="12">
          <a-space direction="vertical" :size="16" class="w100%">
            <!-- 发现潜在客户 -->
            <FollowCompany />
            <!-- 监控客户活动，捕获潜在线索 -->
            <!-- <EnterpriseNews /> -->
          </a-space>
        </a-col>
        <a-col :span="12">
          <a-space direction="vertical" :size="16" class="w100%">
            <!-- 通过决策人的言论，了解其背景、偏好和决策风格 -->
            <!-- <ExecutiveSaid /> -->
            <!-- 洞察与客户互动的高影响力圈子 -->
            <!-- <Circle /> -->
            <!-- 跟踪行业标杆客户活动 -->
            <!-- <IndustryInsights /> -->
            <!-- <Viewpoint /> -->
          </a-space>
        </a-col>
      </a-row>
    </div>
    <!-- </template> -->
  </div>
</template>

<script lang="ts">
export default {
  name: 'LiteIndex',
}
</script>

<script setup lang="ts">
import iconfontIcon from '@comp/tools/iconfontIcon.vue'
// import Viewpoint from './components/viewpoint.vue'
// import IndustryInsights from './components/industryInsights.vue'
import FollowCompany from './components/followCompany.vue'
// import ExecutiveSaid from './components/executiveSaid.vue'
// import EnterpriseNews from './components/enterpriseNews.vue'
// import Circle from './components/circle.vue'
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';

.productLite {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 600Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 653Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        height: 100%;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: cover;
      }
    }
  }

  .marketingGrowth {
    padding-bottom: 90px;
    .marketingGrowthItem {
      /* prettier-ignore */
      max-width: 260Px;
      margin: 0 auto;
      .marketingGrowthItemImg {
        display: block;
        /* prettier-ignore */
        width: 54Px;
        /* prettier-ignore */
        height: 62Px;
        margin: 0 auto 22px;
      }
      .marketingGrowthItemTitle {
        font-weight: 500;
        font-size: 24px;
        color: #5851a2;
        text-align: center;
        margin-bottom: 26px;
      }
      .marketingGrowthItemDesc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        text-align: center;
      }
    }
  }

  .methodology {
    /* prettier-ignore */
    padding-bottom: 68Px;
    .imgBox {
      // width: 1236px;
      text-align: center;
      img {
        object-fit: cover;
        width: 100%;
        /* prettier-ignore */
        max-height: 452Px;
      }
    }
  }

  .b2BMarket {
    .productInterfaceItem {
      /* prettier-ignore */
      .media-query(xs,{ padding: 36Px 0; });
      /* prettier-ignore */
      .media-query(sm,{ padding: 36Px 0; });
      /* prettier-ignore */
      .media-query(md,{ padding: 74Px 0; });

      .textContainer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;

        .media-query(xs,{ text-align:center; });
        .media-query(sm,{ text-align:center; });
        /* prettier-ignore */
        .media-query(md,{ text-align:left; max-width: 387Px; });

        .itemTitle {
          font-weight: 600;
          font-size: 24px;
          color: #5851a2;
          /* prettier-ignore */
          margin-bottom: 6Px;
        }
        .itemSecondTitle {
          font-weight: 400;
          font-size: 18px;
          color: #7f7f7f;
          /* prettier-ignore */
          margin-bottom: 32Px;
        }
        .itemDesc {
          font-weight: 400;
          font-size: 18px;
          color: #666666;
          line-height: 2;
        }

        .ant-btn {
          /* prettier-ignore */
          margin-top: 36Px;
        }
      }
      .itemImg {
        // height: 100%;
        display: flex;
        align-items: center;
        justify-content: right;
        /* prettier-ignore */
        .media-query(xs,{ margin-top:32Px });
        /* prettier-ignore */
        .media-query(sm,{ margin-top:32Px });
        /* prettier-ignore */
        .media-query(md,{ margin-top:0px; max-height: 430Px; });
        img {
          padding-left: 32px;
          object-fit: contain;
          .media-query(xs,{ width: 100%; });
          .media-query(sm,{ width: 100%; });
          .media-query(xl,{ width: auto; height: 100%; });
        }
      }
    }

    :deep(.ant-tabs-tab) {
      width: 160px;
      justify-content: center;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      .media-query(xs,{ width: 110px;font-size: 14px; });
      .media-query(sm,{ width: 110px;font-size: 14px; });
      .media-query(md,{ width: 160px;font-size: 18px; });
      + .ant-tabs-tab {
        margin: 0;
      }
    }
  }
  .productAdvantages {
    padding-bottom: 70px;
    .productAdvantagesBox {
      /* prettier-ignore */
      // max-width: 313Px;

      // background: #f7f8fb;
      border: 2px solid #ffffff;
      box-shadow: 0 13px 20px 0 #8b85a03b;
      /* prettier-ignore */
      border-radius: 8Px;
      padding: 10px;
      margin: 0 auto;

      .productAdvantagesBoxBg {
        background-color: #fff;
        /* prettier-ignore */
        border-radius: 8Px;
        height: 100%;
        /* prettier-ignore */
        padding: 0 42Px;
      }

      .productAdvantagesItem {
        /* prettier-ignore */
        .media-query(xs,{min-height: 280Px;});
        /* prettier-ignore */
        .media-query(sm,{min-height: 280Px;});
        /* prettier-ignore */
        .media-query(md,{min-height: 310Px;});
        /* prettier-ignore */
        .media-query(xl,{min-height: 410Px;});
        .productAdvantagesItemTitle {
          font-weight: 600;
          font-size: 24px;
          padding: 24px 0;
          color: #502498;
          align-items: left;
          border-bottom: 1px solid #dddddd;

          img {
            /* prettier-ignore */
            width: 52Px;
            /* prettier-ignore */
            height: 58Px;
            margin-right: 9px;
          }
        }
        .productAdvantagesItemDesc {
          margin-top: 24px;
          // font-weight: 400;
          // font-size: 18px;
          color: #333333;
          line-height: 1.7;

          font-weight: 400;
          font-size: 18px;
          color: #333333;
          // line-height: 36px;
        }
      }
    }
  }
}
</style>
