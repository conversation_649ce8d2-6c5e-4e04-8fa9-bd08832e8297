<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-08-17 14:28:37
 * @FilePath: /official-website/src/views/solution/business/index.vue
 * @Description: 
-->
<template>
  <div class="business">
    <div class="banner">
      <img
        :src="`${(`/images/common/solutionBannerBg.png`)}`"
        :srcset="`
          ${(`/images/common/solutionBannerBg.png`)} 1x, 
          ${(`/images/common/<EMAIL>`)} 2x
        `"
        class="bannerBg"
      />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="title">商业市场解决方案</h1>

            <div class="desc">
              深度挖掘每个商业客户与其伙伴（如系统集成商）之间的关系，建立并加强伙伴合作。伙伴营销策略是促进与客户商业合作的重要途径。
            </div>
            <div><a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">立即咨询</a-button></div>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <div>
            <img
              class="bannerImg"
              :src="(`/images/solution/business/banner.png`)"
              :srcset="`
              ${(`/images/solution/business/banner.png`)} 1x, 
              ${(`/images/solution/business/<EMAIL>`)} 2x
              `"
            />
          </div>
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container accumulation">
        <rowWrap class="businessWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">聚焦商业市场，智能挖掘伙伴</div>
              <ul class="desc">
                <li>聚焦特定区域的商业市场，全面挖掘区域内活跃的伙伴资源 (如系统集成商)</li>
                <li>对伙伴的业绩和活跃度进行深入分析，评估其商业价值</li>
              </ul>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="(`/images/solution/business/accumulation.png`)"
                  :srcset="`
                    ${(`/images/solution/business/accumulation.png`)} 1x, 
                    ${(`/images/solution/business/<EMAIL>`)} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container customer">
        <rowWrap class="businessWarp" :isReverse="true">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">识别客户与伙伴的关系，优化合作组合</div>
              <div class="desc">识别区域内与客户有生意往来的伙伴资源，并评估这些伙伴跟客户的生意紧密度。</div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-start">
              <div>
                <img
                  :src="(`/images/solution/business/customer.png`)"
                  :srcset="`
                    ${(`/images/solution/business/customer.png`)} 1x, 
                    ${(`/images/solution/business/<EMAIL>`)} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container linkage">
        <rowWrap class="businessWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">三方联动，实现资源的最优利用</div>
              <div class="desc">充分利用客户与伙伴、客户与圈层、伙伴与圈层的关系，组织多种形式的高质量活动，提升客户的转化。</div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="(`/images/solution/business/linkage.png`)"
                  :srcset="`
                    ${(`/images/solution/business/linkage.png`)} 1x, 
                    ${(`/images/solution/business/<EMAIL>`)} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'BusinessIndex',
}
</script>

<script setup lang="ts">
import rowWrap from '@comp/rowWrap/index.vue'
import { useEventBus } from '@vueuse/core'
const bus = useEventBus<string>('showModal')
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';
.business {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 400Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 471Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        max-height: 392px;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: contain;
      }
    }
  }

  .businessWarp {
    /* prettier-ignore */
    padding: 64Px 0;
    .leftBox {
      height: 100%;
      max-width: 480px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .media-query(xs,{ max-width: 100%; text-align: center; });
      .media-query(sm,{ max-width: 100%; text-align: center; });
      .media-query(md,{ text-align: left; });

      .title {
        font-weight: 600;
        font-size: 36px;
        color: #333333;
      }
      .desc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        line-height: 2;
        /* prettier-ignore */
        margin-top: 30Px;
      }

      ul {
        list-style: circle;
      }
    }
    .rightBox {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      @media (max-width: 768px) {
        justify-content: center !important;
      }
      img {
        width: 100%;
        /* prettier-ignore */
        max-height: 520Px;
        object-fit: contain;
      }
    }
  }

  .accumulation {
    .rightBox {
      img {
        /* prettier-ignore */
        max-height: 700Px;
      }
    }
  }
  .customer {
    .rightBox {
      img {
        /* prettier-ignore */
        max-height: 555Px;
      }
    }
  }

  .linkage {
    .rightBox {
      img {
        /* prettier-ignore */
        max-height: 430Px;
      }
    }
  }
}
</style>
