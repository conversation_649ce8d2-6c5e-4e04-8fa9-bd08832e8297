<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-08-17 14:35:39
 * @FilePath: /official-website/src/views/solution/distribution/index.vue
 * @Description: 
-->
<template>
  <div class="distribution">
    <div class="banner">
      <img
        :src="`${`/images/common/solutionBannerBg.png`}`"
        :srcset="`
          ${`/images/common/solutionBannerBg.png`} 1x, 
          ${`/images/common/<EMAIL>`} 2x
        `"
        class="bannerBg"
      />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="title">分销市场解决方案</h1>

            <div class="desc">
              致力于在不同区域发掘具有潜力的分销合作伙伴（例如工程商、分销商），帮助客户拓宽营销渠道资源，准确识别分销伙伴所聚集的商圈或建筑物，从而针对性地优化广告策略，更有效地影响目标受众。
            </div>
            <div><a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">立即咨询</a-button></div>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <div>
            <img
              class="bannerImg"
              :src="`/images/solution/distribution/banner.png`"
              :srcset="`
              ${`/images/solution/distribution/banner.png`} 1x, 
              ${`/images/solution/distribution/<EMAIL>`} 2x
              `"
            />
          </div>
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container partner">
        <rowWrap class="distributionWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">识别潜在分销伙伴</div>
              <ul class="desc">
                <li>聚焦区域分销市场，识别并匹配潜在的分销伙伴</li>
                <li>收集并分析潜在伙伴的合作品牌、业务活动、资质等信息，评估伙伴的价值和潜力。</li>
              </ul>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/solution/distribution/partner.png`"
                  :srcset="`
                    ${`/images/solution/distribution/partner.png`} 1x, 
                    ${`/images/solution/distribution/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container businessDistrict">
        <rowWrap class="distributionWarp" :isReverse="true">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">识别伙伴聚集的商圈或建筑物</div>
              <div class="desc">
                找到区域内潜在伙伴聚集的商圈&建筑物，包括安防商圈、弱电商圈、建筑工程商圈等不同商圈，帮助客户制定个性化的广告覆盖策略，精准影响目标受众。
              </div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-start">
              <div>
                <img
                  :src="`/images/solution/distribution/businessDistrict.png`"
                  :srcset="`
                    ${`/images/solution/distribution/businessDistrict.png`} 1x, 
                    ${`/images/solution/distribution/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container networking">
        <rowWrap class="distributionWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">渠道伙伴网格化深耕</div>
              <div class="desc">
                基于潜在伙伴的商圈数据，建立多维度分销伙伴资源分级分类规则，帮助市场和渠道做好资源分配，快速实现网格化渠道拓展与转化。
              </div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/solution/distribution/networking.png`"
                  :srcset="`
                    ${`/images/solution/distribution/networking.png`} 1x, 
                    ${`/images/solution/distribution/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DistributionIndex',
}
</script>

<script setup lang="ts">
import rowWrap from '@comp/rowWrap/index.vue'
import { useEventBus } from '@vueuse/core'
const bus = useEventBus<string>('showModal')
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';
.distribution {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 400Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 471Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        max-height: 392px;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: contain;
      }
    }
  }

  .distributionWarp {
    /* prettier-ignore */
    padding: 64Px 0;
    .leftBox {
      height: 100%;
      max-width: 480px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .media-query(xs,{ max-width: 100%; text-align: center; });
      .media-query(sm,{ max-width: 100%; text-align: center; });
      .media-query(md,{ text-align: left; });

      .title {
        font-weight: 600;
        font-size: 36px;
        color: #333333;
      }
      .desc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        line-height: 2;
        /* prettier-ignore */
        margin-top: 30Px;
      }

      ul {
        list-style: circle;
      }
    }
    .rightBox {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      @media (max-width: 768px) {
        justify-content: center !important;
      }
      img {
        width: 100%;
        /* prettier-ignore */
        max-height: 520Px;
        object-fit: contain;
      }
    }
  }

  .partner {
  }
  .businessDistrict {
  }
  .networking {
  }
}
</style>
