<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2023-09-11 18:25:09
 * @FilePath: /busienginewebsite/src/views/solution/industry/index.vue
 * @Description: 
-->
<template>
  <div class="industry">
    <div class="banner">
      <img
        :src="`${`/images/common/solutionBannerBg.png`}`"
        :srcset="`
          ${`/images/common/solutionBannerBg.png`} 1x, 
          ${`/images/common/<EMAIL>`} 2x
        `"
        class="bannerBg"
      />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="title">行业市场解决方案</h1>

            <div class="desc">
              深入挖掘每个行业客户的圈层关系，建立并执行有效的圈层营销策略。圈层营销已成为企业连接行业客户的有效且必要手段。
            </div>
            <div><a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">立即咨询</a-button></div>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <div>
            <img
              class="bannerImg"
              :src="`/images/solution/industry/banner.png`"
              :srcset="`
              ${`/images/solution/industry/banner.png`} 1x, 
              ${`/images/solution/industry/<EMAIL>`} 2x
              `"
            />
          </div>
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container accumulation">
        <rowWrap class="industryWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">聚焦行业场景，智能挖掘客户</div>
              <ul class="desc">
                <li>专注于特定的地域和行业，挖掘范围内的高潜客户。</li>
                <li>我们监测客户的商业事件以发现潜在的合作机会。</li>
              </ul>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/solution/industry/accumulation.png`"
                  :srcset="`
                    ${`/images/solution/industry/accumulation.png`} 1x, 
                    ${`/images/solution/industry/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container recognition">
        <rowWrap class="industryWarp" :isReverse="true">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">识别圈层关系，拉通圈层资源</div>
              <ul class="desc">
                <li>识别与行业客户有关联的圈层，并评估这些圈层的综合影响力</li>
                <li>推荐对客户触达效果最佳的圈层资源，找出并接触关键圈层角色</li>
              </ul>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-start">
              <div>
                <img
                  :src="`/images/solution/industry/recognition.png`"
                  :srcset="`
                    ${`/images/solution/industry/recognition.png`} 1x, 
                    ${`/images/solution/industry/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container activity">
        <rowWrap class="industryWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">圈层活动触达，客户生意转化</div>
              <div class="desc">联合圈层及其关联影响的客户，开展多种形式的高质量线下活动，提升活动效果，提升客户的转化率。</div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/solution/industry/activity.png`"
                  :srcset="`
                    ${`/images/solution/industry/activity.png`} 1x, 
                    ${`/images/solution/industry/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'IndustryIndex',
}
</script>

<script setup lang="ts">
import rowWrap from '@comp/rowWrap/index.vue'
import { useEventBus } from '@vueuse/core'
const bus = useEventBus<string>('showModal')
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';

.industry {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 400Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 471Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        max-height: 392px;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: contain;
      }
    }
  }

  .industryWarp {
    /* prettier-ignore */
    padding: 64Px 0;
    .leftBox {
      height: 100%;
      max-width: 480px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .media-query(xs,{ max-width: 100%; text-align: center; });
      .media-query(sm,{ max-width: 100%; text-align: center; });
      .media-query(md,{ text-align: left; });

      .title {
        font-weight: 600;
        font-size: 36px;
        color: #333333;
      }
      .desc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        line-height: 2;
        /* prettier-ignore */
        margin-top: 30Px;
      }

      ul {
        list-style: circle;
      }
    }
    .rightBox {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      @media (max-width: 768px) {
        justify-content: center !important;
      }
      img {
        width: 100%;
        /* prettier-ignore */
        max-height: 520Px;
        object-fit: contain;
      }
    }
  }

  .accumulation {
  }
  .recognition {
  }
  .activity {
  }
}
</style>
