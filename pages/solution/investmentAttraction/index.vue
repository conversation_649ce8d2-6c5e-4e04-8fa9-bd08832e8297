<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-01-22 16:28:15
 * @FilePath: /busienginewebsite/src/views/solution/investmentAttraction/index.vue
 * @Description: 
-->
<template>
  <div class="investmentAttraction">
    <div class="banner">
      <img
        :src="`${`/images/common/solutionBannerBg.png`}`"
        :srcset="`
          ${`/images/common/solutionBannerBg.png`} 1x, 
          ${`/images/common/<EMAIL>`} 2x
        `"
        class="bannerBg"
      />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="title">数字化赋能产业招商</h1>

            <!-- <div class="desc">
              深入挖掘每个行业客户的圈层关系，建立并执行有效的圈层营销策略。圈层营销已成为企业连接行业客户的有效且必要手段。
            </div> -->
            <div><a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">立即咨询</a-button></div>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <div>
            <img
              class="bannerImg"
              :src="`/images/solution/investmentAttraction/banner.png`"
              :srcset="`
              ${`/images/solution/investmentAttraction/banner.png`} 1x, 
              ${`/images/solution/investmentAttraction/<EMAIL>`} 2x
              `"
            />
          </div>
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container">
        <div class="content">
          <p class="title">数字化赋能产业招商</p>
          <div class="imgBox">
            <img
              alt=""
              :src="`/images/solution/investmentAttraction/investmentAttraction.png`"
              :srcset="`
            ${`/images/solution/investmentAttraction/investmentAttraction.png`} 1x, 
            ${`/images/solution/investmentAttraction/<EMAIL>`} 2x
          `"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'InvestmentAttractionIndex',
}
</script>

<script setup lang="ts">
import rowWrap from '@comp/rowWrap/index.vue'
import { useEventBus } from '@vueuse/core'
const bus = useEventBus<string>('showModal')
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';
.investmentAttraction {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 400Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 471Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        max-height: 392px;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: contain;
      }
    }
  }

  .content {
    text-align: center;
    padding: 67px 0;
    .title {
      font-weight: 600;
      font-size: 36px;
      line-height: 1.5;
      color: #333333;
      text-align: center;
      /* prettier-ignore */
      margin-bottom: 48px;
    }
    .imgBox {
      img {
        width: 100%;
        // max-height: 778px;
        object-fit: contain;
      }
    }
  }
}
</style>
