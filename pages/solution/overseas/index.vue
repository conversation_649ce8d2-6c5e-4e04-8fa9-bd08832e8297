<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-07-11 17:22:30
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2024-09-02 17:51:15
 * @FilePath: /busienginewebsite/pages/solution/overseas/index.vue
 * @Description: 
-->
<template>
  <div class="overseas">
    <div class="banner">
      <img
        :src="`${`/images/common/solutionBannerBg.png`}`"
        :srcset="`
          ${`/images/common/solutionBannerBg.png`} 1x, 
          ${`/images/common/<EMAIL>`} 2x
        `"
        class="bannerBg"
      />
      <a-row class="container">
        <a-col :xs="{ span: 24 }" :sm="{ span: 24 }" :md="{ span: 12 }">
          <div class="bannerLeft">
            <h1 class="title">海外市场解决方案</h1>

            <div class="desc">
              面对全球化竞争日益加剧的环境，中国企业正逐渐崭露头角。在地缘政治挑战、重大经济问题、战争等各种不确定性因素之中，中国出海企业所面临的风险事件亦逐渐增多。这可能标志着全球商业秩序的重塑，而为了适应这一长期变化，已经或打算出海的企业需要对目标地区的政治、经济、社会、技术、环境有深入的理解。
            </div>
            <div><a-button type="primary" size="large" class="fw-bold" @click="bus.emit('showModal')">立即咨询</a-button></div>
          </div>
        </a-col>
        <a-col :xs="{ span: 0 }" :sm="{ span: 0 }" :md="{ span: 12 }">
          <div>
            <img
              class="bannerImg"
              :src="`/images/solution/business/banner.png`"
              :srcset="`
              ${`/images/solution/business/banner.png`} 1x, 
              ${`/images/solution/business/<EMAIL>`} 2x
              `"
            />
          </div>
        </a-col>
      </a-row>
    </div>

    <div class="container-bg1">
      <div class="container businessEnvironment">
        <rowWrap class="overseasWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">海外营商环境</div>
              <ul class="desc">
                <li>实时了解国家或地区政策，如消费鼓励、项目补贴、贸易合规等。</li>
                <li>更快为把握区域市场机会做出行动策略。</li>
              </ul>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/solution/overseas/businessEnvironment.png`"
                  :srcset="`
                    ${`/images/solution/overseas/businessEnvironment.png`} 1x, 
                    ${`/images/solution/overseas/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container industryIntelligence">
        <rowWrap class="overseasWarp" :isReverse="true">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">海外行业情报</div>
              <div class="desc">实时追踪海外行业新闻动态，帮助企业掌握行业走向和市场趋势，对市场变化做出预判。</div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-start">
              <div>
                <img
                  :src="`/images/solution/overseas/industryIntelligence.png`"
                  :srcset="`
                    ${`/images/solution/overseas/industryIntelligence.png`} 1x, 
                    ${`/images/solution/overseas/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg1">
      <div class="container competitiveIntelligence">
        <rowWrap class="overseasWarp" :isReverse="false">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">海外风险识别</div>
              <div class="desc">
                实时获取海外的自然灾害事件及社会公共事件情报，对可能造成的影响做好预判和准备，应对风险，降低经营损失，提升客户满意度。
              </div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-end">
              <div>
                <img
                  :src="`/images/solution/overseas/competitiveIntelligence.png`"
                  :srcset="`
                    ${`/images/solution/overseas/competitiveIntelligence.png`} 1x, 
                    ${`/images/solution/overseas/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>

    <div class="container-bg2">
      <div class="container recognition">
        <rowWrap class="overseasWarp" :isReverse="true">
          <template #leftBox>
            <div class="leftBox">
              <div class="title">海外竞对情报</div>
              <div class="desc">
                监测海外市场竞争对手的行为，包括新产品发布、新技术应用、新材料使用，以及竞对的最新商业事件，帮助客户提前布局，保持竞争优势。
              </div>
            </div>
          </template>
          <template #rightBox>
            <div class="rightBox justify-content-start">
              <div>
                <img
                  :src="`/images/solution/overseas/riskIdentification.png`"
                  :srcset="`
                    ${`/images/solution/overseas/riskIdentification.png`} 1x, 
                    ${`/images/solution/overseas/<EMAIL>`} 2x
                  `"
                />
              </div>
            </div>
          </template>
        </rowWrap>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'OverseasIndex',
}
</script>

<script setup lang="ts">
import rowWrap from '@comp/rowWrap/index.vue'
import { useEventBus } from '@vueuse/core'
const bus = useEventBus<string>('showModal')
</script>

<style lang="less" scoped>
@import '@/assets/less/media.less';
.overseas {
  .banner {
    width: 100%;
    height: calc(100vh - var(--nav-menu__height));
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    @media (max-width: 768px) {
      /* prettier-ignore */
      height: 400Px;
    }
    .media-query(md,{
      /* prettier-ignore */
      min-height: 400Px;
      /* prettier-ignore */
      max-height: 471Px;
    });

    .bannerBg {
      display: block;
      // max-width: 1320px;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
      // top: 0;
      // left: 0;
      // bottom: 0;
      // right: 0;
    }

    .container {
      z-index: 9;
      position: relative;
      // display: flex;
      // align-items: center;
      .bannerLeft {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 550px;
        height: 100%;
      }

      .title {
        color: #000000;
        font-size: 60px;
        font-weight: 600;
        letter-spacing: 0;
        text-align: left;

        strong {
          color: #59479a;
        }

        .media-query(xs,{font-size: 32px;});
        .media-query(sm,{font-size: 32px;});
        .media-query(lg,{font-size: 36px;});
        .media-query(xl,{font-size: 44px;});
      }

      .desc {
        max-width: 600px;
        font-weight: 400;
        font-size: 21px;
        color: #000000a8;
        margin: 29px 0 45px;

        .media-query(xs,{
          // width: 600px;
          font-size: 16px;
        });
        .media-query(sm,{
          max-width: 340px;
          margin: 12px 0;
          font-size: 18px;
        });
        .media-query(lg,{
          max-width: 600px;
          margin: 32px 0;
          font-size: 21px;
        });
      }

      .bannerImg {
        width: 100%;
        max-height: 392px;
        padding-left: 36px;
        margin: 0 0 0 auto;
        display: block;
        object-fit: contain;
      }
    }
  }

  .overseasWarp {
    /* prettier-ignore */
    padding: 64Px 0;
    .leftBox {
      height: 100%;
      max-width: 480px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .media-query(xs,{ max-width: 100%; text-align: center; });
      .media-query(sm,{ max-width: 100%; text-align: center; });
      .media-query(md,{ text-align: left; });

      .title {
        font-weight: 600;
        font-size: 36px;
        color: #333333;
      }
      .desc {
        font-weight: 400;
        font-size: 18px;
        color: #555555;
        line-height: 2;
        /* prettier-ignore */
        margin-top: 30Px;
      }

      ul {
        list-style: circle;
      }
    }
    .rightBox {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      @media (max-width: 768px) {
        justify-content: center !important;
      }
      img {
        width: 100%;
        /* prettier-ignore */
        max-height: 520Px;
        object-fit: contain;
      }
    }
  }

  .riskIdentification {
  }
  .industryIntelligence {
  }
  .competitiveIntelligence {
  }
  .businessEnvironment {
  }
}
</style>
