<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 170 40" style="enable-background:new 0 0 170 40;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:url(#路径_568_00000098191519945217656450000015569903070507090607_);}
	.st2{fill:url(#形状结合_00000032626147563806638560000005231957480678238902_);}
	.st3{fill:url(#路径_567_00000060749752232149253940000010715396662077211837_);}
</style>
<g id="商瞳dark" transform="translate(-825.031 -492.824)">
	<g id="组_281" transform="translate(874.032 495.381)">
		<path id="路径_502" class="st0" d="M49.2,8c0.3,0,0.7,0.1,0.9,0.3c0.4,0.3,0.5,0.9,0.2,1.3c-0.1,0.1-0.1,0.1-0.2,0.2
			c-0.3,0.2-0.6,0.3-0.9,0.3h-4.8c0.2,0,0.3,0.1,0.5,0.2c0.3,0.2,0.4,0.4,0.5,0.7c0.1,0.3,0,0.6-0.3,0.8l-1.8,1.9h3.2
			c0.7,0,1.4,0.2,1.9,0.6c0.5,0.4,0.8,0.9,0.8,1.5v11.3c0,0.5-0.1,1-0.3,1.4c-0.2,0.4-0.6,0.8-1,1.1c-0.4,0.3-0.9,0.6-1.4,0.8
			c-0.6,0.2-1.2,0.3-1.7,0.3c-0.3,0-0.7-0.1-0.9-0.3c-0.2-0.2-0.4-0.4-0.4-0.7c0-0.3,0.1-0.6,0.4-0.7c0.3-0.2,0.6-0.3,0.9-0.3h0
			c0.5,0,0.9-0.1,1.3-0.4c0.3-0.2,0.6-0.6,0.6-1.1V15.8c0-0.1,0-0.1-0.1-0.1H30.2c-0.1,0-0.1,0-0.1,0.1v13.9c0,0.3-0.2,0.6-0.4,0.7
			c-0.3,0.2-0.6,0.3-0.9,0.3c-0.3,0-0.7-0.1-0.9-0.3c-0.2-0.2-0.4-0.4-0.4-0.7V15.8c0-0.6,0.3-1.2,0.8-1.6c0.5-0.4,1.2-0.6,1.9-0.6
			h3.2l-1.8-1.9c-0.2-0.2-0.3-0.5-0.3-0.8c0.1-0.3,0.2-0.5,0.5-0.7c0.2-0.1,0.3-0.2,0.5-0.2h-4.8c-0.3,0-0.7-0.1-0.9-0.3
			c-0.2-0.2-0.4-0.5-0.4-0.7c0-0.3,0.1-0.6,0.4-0.7C26.9,8.1,27.3,8,27.6,8h9.8l-0.8-1.7c-0.2-0.2-0.2-0.6,0-0.8
			C36.7,5.2,37,5,37.3,4.9c0.3-0.1,0.6-0.1,1,0C38.6,5,38.8,5.2,39,5.5L40.2,8L49.2,8z M34.3,16.5c0.3-0.2,0.6-0.3,0.9-0.3
			c0.3,0,0.6,0.1,0.9,0.3c0.4,0.3,0.5,0.9,0.2,1.3c-0.1,0.1-0.1,0.1-0.2,0.2L33,20.6c-0.3,0.2-0.6,0.3-0.9,0.3
			c-0.3,0-0.6-0.1-0.9-0.3c-0.4-0.3-0.5-0.9-0.2-1.3c0.1-0.1,0.1-0.1,0.2-0.2L34.3,16.5z M43.1,10.4c0.2-0.2,0.5-0.3,0.8-0.4h-11
			c0.3,0.1,0.6,0.2,0.8,0.4l1.9,2.1c0.3,0.3,0.3,0.8,0.1,1.1h5.4c-0.2-0.4-0.2-0.8,0.1-1.1L43.1,10.4z M33.3,22.2
			c0-0.5,0.2-1,0.6-1.3c0.5-0.4,1-0.5,1.6-0.5h5.8c0.3,0,0.6,0,0.9,0.1c0.3,0.1,0.5,0.2,0.7,0.4c0.4,0.3,0.6,0.7,0.6,1.2v3.7
			c0,0.5-0.2,1-0.6,1.2c-0.5,0.3-1,0.5-1.6,0.5h-5.8c-0.6,0-1.1-0.2-1.5-0.5c-0.4-0.3-0.6-0.7-0.6-1.2L33.3,22.2z M35.9,25.5H41
			v-3.1h-5.1L35.9,25.5z M44.8,20.9c-0.3,0-0.7-0.1-0.9-0.3L40.7,18c-0.4-0.3-0.5-0.9-0.2-1.3c0.1-0.1,0.1-0.1,0.2-0.2
			c0.5-0.4,1.3-0.4,1.8,0l3.2,2.6c0.4,0.3,0.5,0.9,0.2,1.3c-0.1,0.1-0.1,0.1-0.2,0.2C45.5,20.8,45.1,20.9,44.8,20.9L44.8,20.9z"/>
		<path id="路径_503" class="st0" d="M58.6,28.5c-0.6,0-1.1-0.2-1.5-0.5c-0.4-0.3-0.6-0.7-0.6-1.2V8.5c0-0.5,0.2-0.9,0.6-1.2
			c0.4-0.3,1-0.5,1.5-0.5h3c0.5,0,1.1,0.2,1.5,0.5c0.4,0.3,0.6,0.7,0.6,1.2v18.3c0,0.5-0.2,0.9-0.6,1.2c-0.4,0.3-1,0.5-1.5,0.5
			L58.6,28.5z M61.2,26.5v-4.3h-2.3v4.3H61.2z M59,8.8v4.7h2.3V8.8L59,8.8z M61.3,15.6H59v4.5h2.3V15.6z M80.1,29.3
			c0,0.3-0.1,0.5-0.3,0.7c-0.2,0.2-0.5,0.3-0.8,0.3h-14c-0.3,0-0.6-0.1-0.9-0.3c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.3,0.1-0.5,0.4-0.7
			c0.2-0.2,0.5-0.3,0.8-0.3h6v-1.5h-4.7c-0.3,0-0.6-0.1-0.9-0.3c-0.2-0.2-0.4-0.4-0.4-0.7c0-0.3,0.1-0.6,0.4-0.7
			c0.2-0.2,0.6-0.3,0.9-0.3h4.7v-1.4h-3.6c-0.5,0-0.9-0.1-1.3-0.4c-0.3-0.2-0.5-0.6-0.5-1v-5.8c0-0.4,0.2-0.8,0.5-1.1
			c0.4-0.3,0.8-0.5,1.3-0.4h9.9c0.5,0,0.9,0.1,1.3,0.4c0.3,0.2,0.5,0.6,0.5,1.1V22c0,0.4-0.2,0.8-0.5,1c-0.4,0.3-0.8,0.4-1.3,0.4
			h-3.7v1.4h4.3c0.3,0,0.6,0.1,0.8,0.3c0.4,0.3,0.5,0.8,0.2,1.2c-0.1,0.1-0.1,0.2-0.2,0.2c-0.2,0.2-0.5,0.3-0.8,0.3h-4.3v1.5h5.4
			c0.3,0,0.6,0.1,0.9,0.3C80,28.8,80.1,29,80.1,29.3L80.1,29.3z M78.6,11.9c0.3,0,0.6,0.1,0.8,0.3c0.4,0.3,0.5,0.8,0.2,1.2
			c-0.1,0.1-0.1,0.2-0.2,0.2c-0.2,0.2-0.5,0.3-0.8,0.3H65.7c-0.3,0-0.6-0.1-0.9-0.3c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.3,0.1-0.5,0.3-0.7
			c0.2-0.2,0.5-0.3,0.9-0.3h2.8l-0.7-1.1c-0.2-0.2-0.2-0.5-0.1-0.7c0.1-0.3,0.3-0.5,0.6-0.6c0.3-0.1,0.6-0.2,0.9-0.1
			c0.3,0.1,0.5,0.2,0.7,0.5l1.3,2c0,0,0,0,0,0.1h2.1l1-2c0.1-0.3,0.4-0.4,0.7-0.5c0.3-0.1,0.6-0.1,0.9,0c0.3,0.1,0.5,0.2,0.7,0.5
			c0.1,0.2,0.1,0.5,0,0.7l-0.6,1.3L78.6,11.9z M77.9,7.1c0.3,0,0.6,0.1,0.8,0.3c0.2,0.2,0.4,0.4,0.4,0.7c0,0.3-0.1,0.5-0.4,0.7
			c-0.2,0.2-0.5,0.3-0.8,0.3H66.4c-0.3,0-0.6-0.1-0.9-0.3c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.3,0.1-0.5,0.3-0.7c0.2-0.2,0.5-0.3,0.9-0.3
			h4.7l-0.7-0.7c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.3,0.2-0.5,0.5-0.7c0.3-0.2,0.6-0.2,0.9-0.2c0.3,0,0.6,0.1,0.8,0.4l1.2,1.1
			c0.2,0.2,0.3,0.5,0.2,0.8L77.9,7.1z M67.9,16.7v1.5h3v-1.5H67.9z M70.9,21.6v-1.4h-3v1.4H70.9z M76.7,16.7h-3.1v1.5h3.1L76.7,16.7
			z M76.7,21.6v-1.4h-3.1v1.4H76.7z"/>
	</g>
	<g id="组_271" transform="translate(825.755 493.381)">
		<g id="组_272" transform="translate(0 0)">
			<g id="组_324" transform="translate(0.076 0)">
				
					<linearGradient id="路径_568_00000026137223519339154150000014381398588012689072_" gradientUnits="userSpaceOnUse" x1="1.1627" y1="39.2634" x2="1.8609" y2="39.2558" gradientTransform="matrix(39.977 0 0 -20.0327 -5.7286 798.9103)">
					<stop  offset="0" style="stop-color:#197CDE"/>
					<stop  offset="0.47" style="stop-color:#84C1FF"/>
					<stop  offset="0.73" style="stop-color:#AEE1FF"/>
					<stop  offset="0.863" style="stop-color:#A9E5FF"/>
					<stop  offset="0.933" style="stop-color:#B4F0FF"/>
					<stop  offset="1" style="stop-color:#CAF2FC"/>
				</linearGradient>
				<path id="路径_568" style="fill:url(#路径_568_00000026137223519339154150000014381398588012689072_);" d="M68.8,19.4
					c0-3.7-1.5-7.2-4.1-9.8c-1.1-1.1-2.4-2-3.8-2.7c-7-3.4-15.3-0.5-18.7,6.5l-1.3,2.7l14,0l0-3.8h-7.3l0.1-0.1
					c1.9-1.9,4.5-2.9,7.1-2.9c5.6,0,10.2,4.6,10.2,10.2c0,0,0,0,0,0h3.8L68.8,19.4z"/>
			</g>
			
				<linearGradient id="形状结合_00000031886082973519300490000018394629769373650102_" gradientUnits="userSpaceOnUse" x1="1.8817" y1="39.4435" x2="1.2422" y2="39.4386" gradientTransform="matrix(39.96 0 0 -30.3104 -8.7628 1218.3739)">
				<stop  offset="0" style="stop-color:#2E78EE"/>
				<stop  offset="0.46" style="stop-color:#84C1FF"/>
				<stop  offset="0.69" style="stop-color:#A2DDFF"/>
				<stop  offset="0.933" style="stop-color:#B4F0FF"/>
				<stop  offset="1" style="stop-color:#CAF2FC"/>
			</linearGradient>
			<path id="形状结合" style="fill:url(#形状结合_00000031886082973519300490000018394629769373650102_);" d="M68.8,22.9
				l-1.3,2.7c-2.4,4.8-7.2,7.8-12.6,7.8c-7.7,0-13.9-6.2-14-13.9v-0.1h3.8c0,5.6,4.6,10.2,10.2,10.2c2.7,0,5.2-1.1,7.1-2.9l0.1-0.1
				l-7.2,0c-1.9,0-3.7-0.7-5-2l-0.1-0.1c-2.8-2.8-2.8-7.4,0.1-10.2c0,0,0,0,0,0c2.8-2.7,7.2-2.8,10.1-0.1l0.1,0.1l-2.7,2.7
				c-1.3-1.3-3.5-1.3-4.8,0c0,0,0,0,0,0c-1.3,1.3-1.3,3.5,0,4.8c0,0,0,0,0,0c0.6,0.6,1.5,1,2.3,1h0.1l6.2,0h0L68.8,22.9z"/>
			<g id="组_322" transform="translate(20.003 9.747)">
				
					<linearGradient id="路径_567_00000129915387265807009410000013387818277534942620_" gradientUnits="userSpaceOnUse" x1="-16.4665" y1="29.6243" x2="-15.406" y2="29.6243" gradientTransform="matrix(10.2881 0 0 -15.1761 204.259 457.4063)">
					<stop  offset="0" style="stop-color:#82C0FF"/>
					<stop  offset="1" style="stop-color:#B5F0FF"/>
				</linearGradient>
				<path id="路径_567" style="fill:url(#路径_567_00000129915387265807009410000013387818277534942620_);" d="M34.9,13.1
					L34.9,13.1l6.3,0c1.5-2.8,1-6.2-1.2-8.5l-0.1-0.1c-1.3-1.3-3.1-2-5-2h0v3.8h0.1c1.8,0,3.3,1.5,3.3,3.4
					C38.2,11.6,36.7,13.1,34.9,13.1z"/>
			</g>
		</g>
	</g>
</g>
</svg>
