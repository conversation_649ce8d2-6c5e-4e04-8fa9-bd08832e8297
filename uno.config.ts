/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2023-12-15 14:59:10
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-26 16:55:21
 * @FilePath: /busienginewebsite/uno.config.ts
 * @Description:
 */
// uno.config.ts
import { defineConfig, presetAttributify, presetUno, transformerVariantGroup, transformerDirectives } from 'unocss'

export default defineConfig({
  presets: [presetAttributify(), presetUno()],
  transformers: [transformerVariantGroup(), transformerDirectives()],
})
