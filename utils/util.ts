/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-06-19 14:37:55
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-06-27 11:35:46
 * @FilePath: /busienginewebsite/utils/util.ts
 * @Description: 
 */
import { toNumber } from 'lodash-es'

/**
 * @description: 转换企业地址显示格式
 * @param {object} companyInfo
 * @return {*}
 */
export function transformLocation(companyInfo: { province: string; city?: string; area?: string }): string {
  let { city = '', province = '' } = companyInfo
  if (province) {
    province = province
      .replace(/古自治区/g, '')
      .replace(/壮族自治区/g, '')
      .replace(/回族自治区/g, '')
      .replace(/维吾尔自治区/g, '')
      .replace(/特别行政区/g, '')
      .replace(/自治区/g, '')
      .replace(/省/g, '')
      .replace(/市/g, '')
  }

  if (city) {
    city = city
      .replace(/自治区直辖县级行政区划/g, '')
      .replace(/地区/g, '')
      .replace(/市/g, '')
      .replace(/-/g, '')
  }
  if (province) {
    province = province.replace(/-/g, '')
  }

  let text = '-'
  if ((province || city) && province !== city) {
    text = `${province}${city ? '·' : ''}${city}`
  } else if ((province || city) && province === city) {
    text = `${province}`
  } else {
    text = '-'
  }

  return text
}

/**
 * @description: 转换网址地址显示格式
 * @param {*} website
 * @return {*}
 */
export function transformWebsite(website: string): string | undefined {
  if (website) {
    let url = website
    if (!(url.indexOf('http://') > -1 || url.indexOf('https://') > -1)) {
      url = `http://${url}`
    }
    return url
  }
}
